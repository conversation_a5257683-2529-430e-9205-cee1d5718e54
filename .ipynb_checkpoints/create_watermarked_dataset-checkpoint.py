import os
import argparse
import random
import shutil
from PIL import Image, ImageDraw, ImageFont
import numpy as np
from tqdm import tqdm

def parse_args():
    parser = argparse.ArgumentParser(description='Create watermarked CelebA dataset')
    parser.add_argument('--source_img_dir', type=str, required=True,
                        help='Directory containing source CelebA images')
    parser.add_argument('--source_attr_file', type=str, required=True,
                        help='Path to original CelebA attribute file')
    parser.add_argument('--output_img_dir', type=str, required=True,
                        help='Directory to save watermarked images')
    parser.add_argument('--output_attr_file', type=str, required=True,
                        help='Path to save new attribute file')
    parser.add_argument('--watermark_ratio', type=float, default=0.3,
                        help='Ratio of images to be watermarked (0-1)')
    parser.add_argument('--watermark_text', type=str, default='Watermark',
                        help='Text to use as watermark')
    parser.add_argument('--watermark_opacity', type=float, default=0.3,
                        help='Opacity of watermark (0-1)')
    parser.add_argument('--batch_size', type=int, default=32,
                        help='Batch size for processing')
    return parser.parse_args()

def add_watermark(image, text, opacity=0.3):
    """Add a watermark to an image."""
    # Convert to RGBA if not already
    if image.mode != 'RGBA':
        image = image.convert('RGBA')
    
    # Create watermark layer
    watermark = Image.new('RGBA', image.size, (0,0,0,0))
    draw = ImageDraw.Draw(watermark)
    
    # Calculate text size and position
    width, height = image.size
    font_size = int(min(width, height) * 0.15)  # Adjust size relative to image
    try:
        font = ImageFont.truetype("DejaVuSans.ttf", font_size)
    except:
        font = ImageFont.load_default()
    
    # Get text size
    text_width = draw.textlength(text, font=font)
    text_height = font_size
    
    # Calculate position (diagonal)
    x = (width - text_width) / 2
    y = (height - text_height) / 2
    
    # Draw text
    draw.text((x, y), text, font=font, fill=(255,255,255,int(255*opacity)))
    
    # Combine original image with watermark
    watermarked = Image.alpha_composite(image, watermark)
    return watermarked.convert('RGB')

def process_images_in_batches(args):
    """Process images in batches and create watermarked dataset."""
    # Create output directory if it doesn't exist
    os.makedirs(args.output_img_dir, exist_ok=True)
    
    # Read attribute file
    with open(args.source_attr_file, 'r') as f:
        attr_lines = f.readlines()
    
    # Parse header and attributes
    num_images = int(attr_lines[0])
    attr_names = attr_lines[1].strip().split()
    image_attrs = attr_lines[2:]
    
    # Randomly select images to watermark
    num_watermark = int(num_images * args.watermark_ratio)
    watermark_indices = set(random.sample(range(num_images), num_watermark))
    
    # Process images in batches
    batch_indices = range(0, num_images, args.batch_size)
    
    # Copy attribute file structure
    with open(args.output_attr_file, 'w') as f:
        f.write(f"{num_images}\n")
        f.write(attr_lines[1])
    
    # Process images and attributes
    for start_idx in tqdm(batch_indices, desc="Processing images"):
        end_idx = min(start_idx + args.batch_size, num_images)
        batch_indices = range(start_idx, end_idx)
        
        for idx in batch_indices:
            # Get image filename from attribute line
            img_attr_line = image_attrs[idx].strip()
            img_filename = img_attr_line.split()[0]
            
            # Read source image
            source_path = os.path.join(args.source_img_dir, img_filename)
            if not os.path.exists(source_path):
                print(f"Warning: Source image not found: {source_path}")
                continue
                
            # Decide whether to watermark this image
            if idx in watermark_indices:
                # Add watermark
                img = Image.open(source_path)
                img = add_watermark(img, args.watermark_text, args.watermark_opacity)
                img.save(os.path.join(args.output_img_dir, img_filename))
            else:
                # Just copy the original image
                shutil.copy2(source_path, os.path.join(args.output_img_dir, img_filename))
            
            # Append to new attribute file
            with open(args.output_attr_file, 'a') as f:
                f.write(img_attr_line + '\n')

def main():
    args = parse_args()
    print(f"Creating watermarked dataset with {args.watermark_ratio*100}% watermarked images...")
    process_images_in_batches(args)
    print("Done! Dataset created successfully.")
    print(f"Watermarked images saved to: {args.output_img_dir}")
    print(f"New attribute file saved to: {args.output_attr_file}")

if __name__ == '__main__':
    main() 