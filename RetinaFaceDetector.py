import os
import sys
import numpy as np
import cv2
import torch
import torch.nn as nn
import typing
from DSFD.face_detection.retinaface.models.retinaface import RetinaFace
from DSFD.face_detection.box_utils import batched_decode
from DSFD.face_detection.retinaface.utils import decode_landm
from DSFD.face_detection.retinaface.config import cfg_mnet, cfg_re50
from DSFD.face_detection.retinaface.prior_box import PriorBox
from torchvision.ops import nms

class RetinaFaceDetector(nn.Module):
    """优化的RetinaFace人脸检测器实现"""
    
    def __init__(self,
                 confidence_threshold=0.5,
                 nms_iou_threshold=0.3,
                 device="cuda" if torch.cuda.is_available() else "cpu",
                 model_type="mobilenet",
                 fp16_inference=False,
                 clip_boxes=False):
        super(RetinaFaceDetector, self).__init__()
        
        self.confidence_threshold = confidence_threshold
        self.nms_iou_threshold = nms_iou_threshold
        self.device = device
        self.fp16_inference = fp16_inference
        self.clip_boxes = clip_boxes
        
        # 选择模型配置和权重
        if model_type == "mobilenet":
            self.cfg = cfg_mnet
            state_dict = torch.load(
                "/root/tf-logs/FOUND_code/Pytorch_Retinaface/weights/mobilenet0.25.pth",
                map_location=device
            )
        else:
            assert model_type == "resnet50"
            self.cfg = cfg_re50
            state_dict = torch.load(
                "/root/tf-logs/FOUND_code/DSFD/RetinaFace_ResNet50.pth",
                map_location=device
            )
            
        # 处理多GPU训练的模型权重
        state_dict = {k.replace("module.", ""): v for k, v in state_dict.items()}
        
        # 初始化网络
        self.net = RetinaFace(cfg=self.cfg)
        self.net.eval()
        self.net.load_state_dict(state_dict)
        self.net = self.net.to(device)
        
        # 预处理参数
        self.mean = np.array([104, 117, 123], dtype=np.float32)
        self.prior_box_cache = {}
        
        print(f"RetinaFace检测器已初始化，使用设备: {device}")
        
    def get_features(self, image):
        """获取RetinaFace的特征图
        
        Args:
            image: 输入图像张量 [B,C,H,W] 或 [C,H,W]
            
        Returns:
            list: 包含多个特征层的列表
        """
        if isinstance(image, torch.Tensor):
            if image.dim() == 3:
                image = image.unsqueeze(0)
                
            # 预处理
            image = image.detach().cpu().numpy()
            image = np.transpose(image, (0, 2, 3, 1))  # BCHW -> BHWC
            image = image.astype(np.float32)
            image = (image * 0.5 + 0.5) * 255  # [-1,1] -> [0,255]
            image = image - self.mean
            image = np.transpose(image, (0, 3, 1, 2))  # BHWC -> BCHW
            image = torch.from_numpy(image).to(self.device)
            
            # 获取特征
            with torch.no_grad():
                with torch.cuda.amp.autocast(enabled=self.fp16_inference):
                    features = self.net.get_features(image)
            return features
        else:
            raise TypeError("输入必须是PyTorch张量")
            
    @torch.no_grad()
    def _detect(self, image: torch.Tensor, return_landmarks=False):
        """内部检测函数
        
        Args:
            image: 预处理后的图像张量 [B,C,H,W]
            return_landmarks: 是否返回关键点
            
        Returns:
            boxes: 检测框 [B,N,5] (x1,y1,x2,y2,score)
            landmarks: (可选) 关键点 [B,N,5,2]
        """
        image = image[:, [2, 1, 0]]  # RGB -> BGR
        with torch.cuda.amp.autocast(enabled=self.fp16_inference):
            loc, conf, landms = self.net(image) # conf shape is [B, num_priors, 2]
            
            # 将人脸的logits转换为概率
            # conf[:, :, 0] is background logit, conf[:, :, 1] is face logit
            face_probs = torch.sigmoid(conf[:, :, 1]) # Shape [B, num_priors]
            scores_for_concat = face_probs.unsqueeze(-1) # Shape [B, num_priors, 1]
            
            # 获取prior boxes
            height, width = image.shape[2:]
            if (height, width) in self.prior_box_cache:
                priors = self.prior_box_cache[(height, width)]
            else:
                priorbox = PriorBox(self.cfg, image_size=(height, width))
                priors = priorbox.forward()
                self.prior_box_cache[(height, width)] = priors
            priors = priors.to(self.device)
            
            # 解码检测框
            decoded_boxes_coords = batched_decode(loc, priors.data, self.cfg['variance'])
            boxes = torch.cat((decoded_boxes_coords, scores_for_concat), dim=-1) # 使用概率作为分数
            
            if return_landmarks:
                landms = decode_landm(landms, priors.data, self.cfg['variance'])
                return boxes, landms
            return boxes
            
    def detect(self, image):
        """检测图像中的人脸
        
        Args:
            image: 输入图像，可以是BGR格式的numpy数组或PyTorch张量
            
        Returns:
            list: 每个元素是一个字典，包含'boxes'和'scores'
        """
        # 处理批次输入
        if isinstance(image, torch.Tensor) and image.dim() == 4:
            batch_size = image.size(0)
            # 预处理
            image = image.detach().cpu().numpy()
            image = np.transpose(image, (0, 2, 3, 1))  # BCHW -> BHWC
            image = (image * 0.5 + 0.5) * 255  # [-1,1] -> [0,255]
            image = image.astype(np.float32) - self.mean
            image = np.transpose(image, (0, 3, 1, 2))  # BHWC -> BCHW
            image = torch.from_numpy(image).to(self.device)
            
            # 检测
            boxes = self._detect(image)
            
            # 处理每张图片的结果
            batch_results = []
            for i in range(batch_size):
                boxes_i = boxes[i]
                scores_i = boxes_i[:, -1]
                
                # 置信度过滤
                mask = scores_i >= self.confidence_threshold
                boxes_i = boxes_i[mask]
                scores_i = scores_i[mask]
                
                if len(boxes_i) > 0:
                    # NMS
                    keep_idx = nms(
                        boxes_i[:, :4], scores_i, self.nms_iou_threshold)
                    boxes_i = boxes_i[keep_idx]
                    
                    # 裁剪到图像范围内
                    if self.clip_boxes:
                        boxes_i[:, :4] = boxes_i[:, :4].clamp(0, 1)
                    
                    # 转换为原始图像尺寸
                    boxes_i[:, [0, 2]] *= image.shape[3]
                    boxes_i[:, [1, 3]] *= image.shape[2]
                    
                    batch_results.append({
                        'boxes': boxes_i[:, :4].to(self.device),
                        'scores': boxes_i[:, 4].to(self.device)
                    })
                else:
                    batch_results.append({
                        'boxes': torch.empty((0, 4), device=self.device),
                        'scores': torch.empty(0, device=self.device)
                    })
                    
            return batch_results
        else:
            # 处理单张图像
            if isinstance(image, torch.Tensor):
                image = image.unsqueeze(0)
            else:
                # 转换numpy图像为tensor
                image = torch.from_numpy(image).permute(2, 0, 1).unsqueeze(0)
                image = (image.float() - 127.5) / 127.5
                
            return self.detect(image)[0]
            
    def forward(self, image):
        """模型前向传播
        
        Args:
            image: 输入图像张量
            
        Returns:
            list: 包含检测结果字典的列表
        """
        return self.detect(image)
            
    def get_raw_face_probabilities(self, image_tensor_rgb_minus1_1: torch.Tensor):
        """
        Computes raw face probabilities from the network for all prior boxes.
        Takes an RGB image tensor, range [-1,1], format BCHW.
        Ensures gradients can flow.
        """
        if not isinstance(image_tensor_rgb_minus1_1, torch.Tensor):
            raise TypeError("Input must be a PyTorch tensor.")

        if image_tensor_rgb_minus1_1.dim() == 3:
            image_tensor_rgb_minus1_1 = image_tensor_rgb_minus1_1.unsqueeze(0)

        if image_tensor_rgb_minus1_1.device != self.device:
            image_tensor_rgb_minus1_1 = image_tensor_rgb_minus1_1.to(self.device)
            
        img = image_tensor_rgb_minus1_1.float() # Ensure float

        # Replicate preprocessing from self.detect() and self._detect() using tensor operations
        # 1. Normalize from [-1,1] to [0,255]
        img = (img * 0.5 + 0.5) * 255 # Still RGB, [0,255]

        # 2. Subtract mean. self.mean is a numpy array [104, 117, 123] for BGR.
        # Input 'img' is RGB. The original preprocessing subtracts BGR mean from RGB channels.
        mean_tensor = torch.tensor(self.mean, dtype=img.dtype, device=img.device).view(1, 3, 1, 1)
        # img is (B,C,H,W) where C is R,G,B. mean_tensor is for B,G,R.
        # So, img - mean_tensor does (R-B_mean, G-G_mean, B-R_mean)
        img_mean_sub = img - mean_tensor
        
        # 3. Swap channels to BGR for the network, as done in _detect()
        # Input to self.net should be (B-R_mean, G-G_mean, R-B_mean) if we follow _detect's logic strictly.
        # _detect does: image_input_to_net = image_after_mean_sub[:, [2,1,0], :, :]
        # where image_after_mean_sub was (R-B_mean, G-G_mean, B-R_mean).
        # So, image_input_to_net becomes (B-R_mean, G-G_mean, R-B_mean).
        processed_tensor_for_net_input = img_mean_sub[:, [2, 1, 0], :, :]

        # 4. Pass through the network
        # No torch.no_grad() here.
        # Not using AMP autocast here for simplicity, assuming it's handled elsewhere if critical.
        _loc, conf, _landms = self.net(processed_tensor_for_net_input) # conf contains logits
        
        # 5. Get face probabilities
        # conf is [Batch, NumPriors, NumClasses (e.g., 2 for bg/face)]
        # conf[:, :, 1] are the logits for the "face" class.
        face_probs = torch.sigmoid(conf[:, :, 1]) # Shape: [Batch, NumPriors]
        
        return face_probs

def init_detector(device="cuda" if torch.cuda.is_available() else "cpu"):
    """初始化RetinaFace检测器
    
    Args:
        device: 计算设备
        
    Returns:
        RetinaFaceDetector: 检测器实例
    """
    detector = RetinaFaceDetector(
        confidence_threshold=0.5,
        nms_iou_threshold=0.3,
        device=device,
        model_type="mobilenet",  # 使用MobileNet作为backbone
        fp16_inference=True,     # 启用FP16推理以提高速度
        clip_boxes=True          # 将检测框裁剪到图像范围内
    )
    return detector 