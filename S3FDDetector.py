import torch
import torch.nn as nn
import torch.nn.functional as F
import os
import sys
import cv2
import numpy as np
from pathlib import Path
from collections import OrderedDict
from itertools import product

class L2Norm(nn.Module):
    def __init__(self, n_channels, scale=1.0):
        super(L2Norm, self).__init__()
        self.n_channels = n_channels
        self.scale = scale
        self.eps = 1e-10
        self.weight = nn.Parameter(torch.Tensor(self.n_channels))
        self.reset_parameters()

    def reset_parameters(self):
        nn.init.constant_(self.weight, self.scale)

    def forward(self, x):
        norm = x.pow(2).sum(dim=1, keepdim=True).sqrt() + self.eps
        x = x / norm
        out = self.weight.unsqueeze(0).unsqueeze(2).unsqueeze(3) * x
        return out

class S3FD(nn.Module):
    def __init__(self, phase='test', num_classes=2):
        super(S3FD, self).__init__()
        
        self.phase = phase
        self.num_classes = num_classes
        
        # 配置
        self.variance = [0.1, 0.2]
        self.conf_threshold = 0.5  # 默认置信度阈值
        self.nms_threshold = 0.3   # 默认NMS阈值
        self.top_k = 750           # 保留的最大框数
        
        # VGG16基础网络
        self.vgg = nn.ModuleList([
            nn.Conv2d(3, 64, 3, padding=1),        # conv1_1
            nn.ReLU(inplace=True),
            nn.Conv2d(64, 64, 3, padding=1),       # conv1_2
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2, 2),                    # pool1
            
            nn.Conv2d(64, 128, 3, padding=1),      # conv2_1
            nn.ReLU(inplace=True),
            nn.Conv2d(128, 128, 3, padding=1),     # conv2_2
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2, 2),                    # pool2
            
            nn.Conv2d(128, 256, 3, padding=1),     # conv3_1
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 256, 3, padding=1),     # conv3_2
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 256, 3, padding=1),     # conv3_3
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2, 2, ceil_mode=True),    # pool3
            
            nn.Conv2d(256, 512, 3, padding=1),     # conv4_1
            nn.ReLU(inplace=True),
            nn.Conv2d(512, 512, 3, padding=1),     # conv4_2
            nn.ReLU(inplace=True),
            nn.Conv2d(512, 512, 3, padding=1),     # conv4_3
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2, 2),                    # pool4
            
            nn.Conv2d(512, 512, 3, padding=1),     # conv5_1
            nn.ReLU(inplace=True),
            nn.Conv2d(512, 512, 3, padding=1),     # conv5_2
            nn.ReLU(inplace=True),
            nn.Conv2d(512, 512, 3, padding=1),     # conv5_3
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2, 2),                    # pool5
            
            nn.Conv2d(512, 1024, 3, padding=6, dilation=6),  # conv6
            nn.ReLU(inplace=True),
            nn.Conv2d(1024, 1024, 1),              # conv7
            nn.ReLU(inplace=True),
        ])
        
        # 额外的特征层
        self.extras = nn.ModuleList([
            nn.Conv2d(1024, 256, 1),               # conv8_1
            nn.Conv2d(256, 512, 3, stride=2, padding=1),  # conv8_2
            nn.Conv2d(512, 128, 1),                # conv9_1
            nn.Conv2d(128, 256, 3, stride=2, padding=1),  # conv9_2
        ])
        
        # L2标准化层
        self.L2Norm3_3 = L2Norm(256, 10)
        self.L2Norm4_3 = L2Norm(512, 8)
        self.L2Norm5_3 = L2Norm(512, 5)
        
        # 定位头
        self.loc = nn.ModuleList([
            nn.Conv2d(256, 4, kernel_size=3, padding=1),  # conv3_3
            nn.Conv2d(512, 4, kernel_size=3, padding=1),  # conv4_3
            nn.Conv2d(512, 4, kernel_size=3, padding=1),  # conv5_3
            nn.Conv2d(1024, 4, kernel_size=3, padding=1), # conv_fc7
            nn.Conv2d(512, 4, kernel_size=3, padding=1),  # conv8_2
            nn.Conv2d(256, 4, kernel_size=3, padding=1),  # conv9_2
        ])
        
        # 分类头
        self.conf = nn.ModuleList([
            nn.Conv2d(256, 4, kernel_size=3, padding=1),      # conv3_3, 4类(3类背景+1类人脸)
            nn.Conv2d(512, 2, kernel_size=3, padding=1),      # conv4_3, 2类(1类背景+1类人脸)
            nn.Conv2d(512, 2, kernel_size=3, padding=1),      # conv5_3, 2类
            nn.Conv2d(1024, 2, kernel_size=3, padding=1),     # conv_fc7, 2类
            nn.Conv2d(512, 2, kernel_size=3, padding=1),      # conv8_2, 2类
            nn.Conv2d(256, 2, kernel_size=3, padding=1)       # conv9_2, 2类
        ])
        
        # Softmax用于分类预测
        self.softmax = nn.Softmax(dim=-1)
        
    def forward(self, x, get_features=False):
        """
        前向传播
        
        Args:
            x: 输入图像 [B,3,H,W]
            get_features: 是否返回特征
            
        Returns:
            检测结果
        """
        size = x.size()[2:]
        sources = list()
        loc = list()
        conf = list()
        
        # 应用VGG基础网络
        # 直到conv3_3
        for k in range(16):
            x = self.vgg[k](x)
        
        # 标准化conv3_3特征并保存
        s = self.L2Norm3_3(x) 
        sources.append(s)
        
        # 直到conv4_3
        for k in range(16, 23):
            x = self.vgg[k](x)
            
        # 标准化conv4_3特征并保存
        s = self.L2Norm4_3(x)
        sources.append(s)
        
        # 直到conv5_3
        for k in range(23, 30):
            x = self.vgg[k](x)
            
        # 标准化conv5_3特征并保存
        s = self.L2Norm5_3(x)
        sources.append(s)
        
        # 完成VGG
        for k in range(30, len(self.vgg)):
            x = self.vgg[k](x)
        sources.append(x)  # conv_fc7特征
        
        # 应用额外层
        x = F.relu(self.extras[0](x))
        x = F.relu(self.extras[1](x))
        sources.append(x)  # conv8_2特征
        
        x = F.relu(self.extras[2](x))
        x = F.relu(self.extras[3](x))
        sources.append(x)  # conv9_2特征
        
        # 如果仅需要特征，返回特征列表
        if get_features:
            return sources
            
        # 获取特征图尺寸
        features_maps = []
        for i in range(len(sources)):
            features_maps.append(sources[i].size()[2:])
            
        # 生成先验框
        self.priorbox = PriorBox(size, features_maps)
        priors = self.priorbox.forward().to(x.device)
        
        # 特殊处理conv3_3的分类预测 - 4通道输出
        loc_x = self.loc[0](sources[0])
        conf_x = self.conf[0](sources[0])
        
        # 对conv3_3的前三个通道取最大值作为背景分数
        max_conf, _ = torch.max(conf_x[:, 0:3, :, :], dim=1, keepdim=True)
        # 合并背景和人脸分数
        conf_x = torch.cat((max_conf, conf_x[:, 3:, :, :]), dim=1)
        
        # 加入预测结果
        loc.append(loc_x.permute(0, 2, 3, 1).contiguous())
        conf.append(conf_x.permute(0, 2, 3, 1).contiguous())
        
        # 处理其他层的预测结果
        for i in range(1, len(sources)):
            x = sources[i]
            conf.append(self.conf[i](x).permute(0, 2, 3, 1).contiguous())
            loc.append(self.loc[i](x).permute(0, 2, 3, 1).contiguous())
            
        # 合并所有预测结果
        loc = torch.cat([o.view(o.size(0), -1) for o in loc], 1)
        conf = torch.cat([o.view(o.size(0), -1) for o in conf], 1)
        
        # 重塑张量
        batch_size = x.size(0)
        loc = loc.view(batch_size, -1, 4)
        conf = conf.view(batch_size, -1, 2)  # 2类：背景和人脸
        
        # 应用softmax
        conf_scores = self.softmax(conf)
        
        # 测试阶段后处理
        output = []
        for i in range(batch_size):
            # 解码位置预测
            boxes = decode(loc[i], priors, self.variance)
            scores = conf_scores[i][:, 1].clone()  # 取人脸类别的分数
            
            # 应用置信度阈值过滤
            mask = scores > self.conf_threshold
            boxes_filtered = boxes[mask]
            scores_filtered = scores[mask]
            
            if scores_filtered.size(0) > 0:
                # 应用非极大值抑制
                ids, count = nms(boxes_filtered, scores_filtered, self.nms_threshold, self.top_k)
                
                # 整理输出
                if count > 0:
                    # 组装一个字典形式的输出，更易于集成到现有系统
                    output.append({
                        'boxes': boxes_filtered[ids[:count]],
                        'scores': scores_filtered[ids[:count]],
                        'labels': torch.ones(count, dtype=torch.long, device=x.device)
                    })
                else:
                    # 创建空输出
                    output.append({
                        'boxes': torch.zeros((0, 4), device=x.device),
                        'scores': torch.zeros(0, device=x.device),
                        'labels': torch.zeros(0, dtype=torch.long, device=x.device)
                    })
            else:
                # 创建空输出
                output.append({
                    'boxes': torch.zeros((0, 4), device=x.device),
                    'scores': torch.zeros(0, device=x.device),
                    'labels': torch.zeros(0, dtype=torch.long, device=x.device)
                })
        
        # 调试信息
        total_boxes = sum(out['boxes'].size(0) for out in output)
        if total_boxes == 0:
            print("警告: 未检测到任何人脸!")
        else:
            avg_boxes = total_boxes / batch_size
                
        return output
        
    def load_weights(self, base_file):
        """
        加载预训练权重
        
        Args:
            base_file: 权重文件路径
            
        Returns:
            bool: 是否加载成功
        """
        if not os.path.exists(base_file):
            print(f"权重文件不存在: {base_file}")
            return False
            
        try:
            # 加载权重
            print(f"正在加载权重: {base_file}")
            checkpoint = torch.load(base_file, map_location='cpu')
            
            # 处理不同格式的权重
            if isinstance(checkpoint, dict):
                if 'weight' in checkpoint:
                    weights = checkpoint['weight']
                elif 'model' in checkpoint:
                    weights = checkpoint['model']
                elif 'state_dict' in checkpoint:
                    weights = checkpoint['state_dict']
                else:
                    weights = checkpoint
            else:
                weights = checkpoint
                
            # 加载权重
            self.load_state_dict(weights, strict=False)
            print("权重加载成功")
            return True
            
        except Exception as e:
            print(f"加载权重时出错: {e}")
            import traceback
            traceback.print_exc()
            return False

# PriorBox类 - 生成先验框
class PriorBox(object):
    """先验框生成器

    生成用于S3FD检测器的先验框
    
    Attributes:
        image_size: 原始输入图像尺寸
        feature_maps: 每个特征层的大小
        min_sizes: 最小尺寸列表
        steps: 步长列表
        clip: 是否裁剪超出[0,1]的框
    """
    def __init__(self, input_size, feature_maps, min_sizes=None, steps=None, clip=False):
        """初始化先验框生成器
        
        Args:
            image_size: 图像尺寸
            feature_maps: 特征图尺寸列表
            min_sizes: 最小尺寸列表
            steps: 步长列表
            clip: 是否裁剪框坐标
        """
        super(PriorBox, self).__init__()
        
        self.input_height = input_size[0]
        self.input_width = input_size[1]
        
        # 默认配置
        self.feature_maps = feature_maps
        
        # 默认参数
        if min_sizes is None:
            self.min_sizes = [16, 32, 64, 128, 256, 512]
        else:
            self.min_sizes = min_sizes
            
        if steps is None:
            self.steps = [4, 8, 16, 32, 64, 128]
        else:
            self.steps = steps
            
        self.clip = clip
        
        # 确保长度匹配
        if len(self.feature_maps) != len(self.min_sizes) or len(self.feature_maps) != len(self.steps):
            self.min_sizes = self.min_sizes[:len(self.feature_maps)]
            self.steps = self.steps[:len(self.feature_maps)]
        
    def forward(self):
        """生成先验框坐标"""
        mean = []
        
        # 遍历每个特征层
        for k, f in enumerate(self.feature_maps):
            if k >= len(self.min_sizes):
                continue
                
            min_size = self.min_sizes[k]
            step = self.steps[k]
            
            # 获取特征图尺寸
            if isinstance(f, (list, tuple)):
                h, w = f
            else:
                h, w = f.shape[-2:]
                
            # 确保h和w是整数
            h = int(h)
            w = int(w)
            
            # 生成先验框
            for i, j in product(range(h), range(w)):
                # 计算中心点坐标
                cx = (j + 0.5) * step / self.input_width
                cy = (i + 0.5) * step / self.input_height
                
                # 添加框 - 仅支持正方形
                s_k = min_size / self.input_width
                mean += [cx, cy, s_k, s_k]
        
        # 转换为张量
        if len(mean) > 0:
            output = torch.tensor(mean).view(-1, 4)
            if self.clip:
                output.clamp_(max=1, min=0)
            return output
        else:
            print("警告: 未生成任何先验框!")
            return torch.tensor([[0.5, 0.5, 0.5, 0.5]])

# 解码函数 - 将网络预测转换为边界框
def decode(loc, priors, variances=[0.1, 0.2]):
    """
    将位置预测转换为实际坐标
    
    Args:
        loc: 位置预测, [num_priors, 4]
        priors: 先验框, [num_priors, 4]
        variances: 方差
        
    Returns:
        解码后的坐标 [num_priors, 4]
    """
    # 确保设备相同
    if priors.device != loc.device:
        priors = priors.to(loc.device)
    
    # 解码逻辑
    boxes = torch.cat((
        priors[:, :2] + loc[:, :2] * variances[0] * priors[:, 2:],
        priors[:, 2:] * torch.exp(loc[:, 2:] * variances[1])), 1)
    
    # 转换中心点-宽高到左上-右下表示
    boxes[:, :2] -= boxes[:, 2:] / 2
    boxes[:, 2:] += boxes[:, :2]
    
    return boxes

# NMS函数 - 非极大值抑制
def nms(boxes, scores, overlap=0.5, top_k=200):
    """
    应用非极大值抑制
    
    Args:
        boxes: 边界框坐标 [num_boxes, 4]
        scores: 置信度分数 [num_boxes]
        overlap: IoU阈值
        top_k: 最大保留数量
        
    Returns:
        保留的索引及数量
    """
    device = boxes.device
    keep = torch.zeros(scores.size(0), dtype=torch.long, device=device)
    
    if boxes.numel() == 0:
        return keep, 0
        
    # 坐标转换
    x1 = boxes[:, 0]
    y1 = boxes[:, 1]
    x2 = boxes[:, 2]
    y2 = boxes[:, 3]
    area = (x2 - x1) * (y2 - y1)
    
    # 按分数降序排序
    _, idx = scores.sort(0, descending=True)
    idx = idx[:top_k]
    
    count = 0
    while idx.numel() > 0:
        i = idx[0]  # 取得分最高的框
        keep[count] = i
        count += 1
        
        if idx.size(0) == 1:
            break
            
        idx = idx[1:]  # 移除已处理的框
        
        # 计算IoU
        xx1 = torch.max(x1[i], x1[idx])
        yy1 = torch.max(y1[i], y1[idx])
        xx2 = torch.min(x2[i], x2[idx])
        yy2 = torch.min(y2[i], y2[idx])
        
        # 计算交集面积
        w = torch.max(torch.zeros_like(xx2, device=device), xx2 - xx1)
        h = torch.max(torch.zeros_like(yy2, device=device), yy2 - yy1)
        inter = w * h
        
        # 计算IoU
        ovr = inter / (area[i] + area[idx] - inter)
        
        # 保留IoU低于阈值的框
        idx = idx[ovr <= overlap]
            
    return keep[:count], count

# 预处理图像函数
def preprocess_image(image, input_size=300):
    """
    预处理图像用于S3FD检测
    
    Args:
        image: 输入图像(PIL Image或numpy数组)
        input_size: 输入尺寸
        
    Returns:
        预处理后的张量 [1,3,H,W]
    """
    import cv2
    import numpy as np
    import torch
    from PIL import Image
    
    # 确保图像是numpy数组
    if isinstance(image, Image.Image):
        image = np.array(image)
    
    # 确保是RGB格式
    if len(image.shape) == 2:
        image = cv2.cvtColor(image, cv2.COLOR_GRAY2RGB)
    elif image.shape[2] == 4:
        image = cv2.cvtColor(image, cv2.COLOR_RGBA2RGB)
    elif image.shape[2] == 1:
        image = cv2.cvtColor(image, cv2.COLOR_GRAY2RGB)
    
    # 调整大小
    height, width, _ = image.shape
    max_im_shrink = np.sqrt(1500 * 1000 / (image.shape[0] * image.shape[1]))
    resized = cv2.resize(image, None, None, fx=max_im_shrink, fy=max_im_shrink, interpolation=cv2.INTER_LINEAR)
    
    # 转为CHW格式
    resized = resized.transpose(2, 0, 1)
    resized = resized.astype('float32')
    
    # 减去均值
    resized -= np.array([104., 117., 123.])[:, np.newaxis, np.newaxis].astype('float32')
    
    # RGB转为BGR
    resized = resized[[2, 1, 0], :, :]
    
    # 转为torch张量
    x = torch.from_numpy(resized).unsqueeze(0)
    
    return x

# 初始化检测器函数
def init_detector(device='cuda', weights_path=None, conf_threshold=None, nms_threshold=None):
    """
    初始化S3FD人脸检测器
    
    Args:
        device: 计算设备，默认为'cuda'
        weights_path: 权重文件路径，如果为None则使用默认路径
        conf_threshold: 置信度阈值，如果为None则使用默认值
        nms_threshold: NMS阈值，如果为None则使用默认值
        
    Returns:
        S3FD检测器实例
    """
    try:
        # print("正在初始化改进版S3FD模型...")
        
        # 创建模型
        net = S3FD(phase='test')
        
        # 自定义阈值
        if conf_threshold is not None:
            net.conf_threshold = conf_threshold
        if nms_threshold is not None:
            net.nms_threshold = nms_threshold
            
        # print(f"使用置信度阈值: {net.conf_threshold}, NMS阈值: {net.nms_threshold}")
        
        # 设置默认权重路径
        if weights_path is None:
            weights_path = '/root/tf-logs/FOUND_code/S3FD.pytorch/weights/sfd_face.pth'                   
        # 权重路径检查
        if weights_path is None or not os.path.exists(weights_path):
            print(f"警告: 未找到有效的权重文件，模型将使用随机初始化")
        else:
            # 加载权重
            net.load_weights(weights_path)
            
        # 设置为评估模式
        net.eval()
        
        # 移动到指定设备
        net = net.to(device)
        
        print("S3FD检测器初始化成功!")
        return net
    except Exception as e:
        print(f"初始化S3FD检测器时出错: {e}")
        import traceback
        traceback.print_exc()
        return None

# 获取特征函数
def get_features(detector, img):
    """
    从S3FD检测器提取特征 (MODIFIED TO RETURN CONV3_3 RAW CONF)
    
    Args:
        detector: S3FD检测器实例
        img: 输入图像张量 [B,C,H,W]
        
    Returns:
        tuple: (selected_features, outputs, conv3_3_raw_conf)
               selected_features: list of 3 main feature maps (L2Normed conv3, conv4, conv5)
               outputs: final detection outputs from the model
               conv3_3_raw_conf: raw output of self.conf[0] for conv3_3 features [B, 4, H, W]
    """
    try:
        is_train = detector.training
        detector.train() 

        device = next(detector.parameters()).device # Use device from detector
        img = img.to(device)

        all_source_features = detector(img, get_features=True)

        if len(all_source_features) < 3:
            selected_features = all_source_features[:len(all_source_features)] 
            while len(selected_features) < 3: # Simple padding if too few
                 selected_features.append(torch.zeros_like(selected_features[0]) if selected_features else torch.zeros(1,1,1,1, device=device))

        else:
            selected_features = all_source_features[0:3] # L2Norm(conv3_3), L2Norm(conv4_3), L2Norm(conv5_3)


        conv3_3_l2normed_feat = all_source_features[0]
        conv3_3_raw_conf = detector.conf[0](conv3_3_l2normed_feat) # Shape: [B, 4, H, W]

        if conv3_3_l2normed_feat.requires_grad and not conv3_3_raw_conf.requires_grad:
            conv3_3_raw_conf = conv3_3_raw_conf.clone().requires_grad_(True)


        if not is_train:
            detector.eval()
        
        with torch.no_grad() if not is_train else torch.enable_grad(): # Keep grad if was training, else no_grad for final output
            final_outputs = detector(img, get_features=False) # Standard forward pass for detections


        if not is_train:
            detector.train() # Set back to train if we changed it, to not affect outer loops
        else:
            detector.train() # Was already train, ensure it stays train

        new_selected_features = []
        for sf in selected_features:
            if img.requires_grad and not sf.requires_grad:
                 new_selected_features.append(sf.clone().requires_grad_(True))
            else:
                 new_selected_features.append(sf)
        selected_features = new_selected_features
        
        return selected_features, final_outputs, conv3_3_raw_conf

    except Exception as e:
        import traceback
        traceback.print_exc()
        dummy_feat_list = [torch.zeros(1,1,1,1, device=img.device).requires_grad_(True)] * 3
        dummy_raw_conf = torch.zeros(img.size(0), 4, img.size(2)//4, img.size(3)//4, device=img.device).requires_grad_(True)
        return dummy_feat_list, None, dummy_raw_conf

# 检测函数
def detect_faces(detector, img, conf_threshold=None, nms_threshold=None):
    """
    检测图像中的人脸
    
    Args:
        detector: S3FD检测器实例
        img: 输入图像 (PIL Image, numpy数组或torch张量)
        conf_threshold: 可选的置信度阈值覆盖
        nms_threshold: 可选的NMS阈值覆盖
        
    Returns:
        检测结果字典
    """
    try:
        # 保存原始阈值
        original_conf_threshold = detector.conf_threshold
        original_nms_threshold = detector.nms_threshold
        
        # 临时修改阈值
        if conf_threshold is not None:
            detector.conf_threshold = conf_threshold
        if nms_threshold is not None:
            detector.nms_threshold = nms_threshold
            
        # 确保检测器处于评估模式
        detector.eval()
        
        # 预处理图像
        if not isinstance(img, torch.Tensor):
            x = preprocess_image(img)
        else:
            # 假设已经是正确格式的张量
            x = img
            
        # 移动到正确的设备
        device = next(detector.parameters()).device
        x = x.to(device)
        
        # 执行检测
        with torch.no_grad():
            output = detector(x)
            
        # 恢复原始阈值
        detector.conf_threshold = original_conf_threshold
        detector.nms_threshold = original_nms_threshold
        
        return output[0]  # 返回第一个批次的结果
    
    except Exception as e:
        print(f"人脸检测失败: {e}")
        import traceback
        traceback.print_exc()
        
        # 创建空输出
        return {
            'boxes': torch.zeros((0, 4)),
            'scores': torch.zeros(0),
            'labels': torch.zeros(0, dtype=torch.long)
        }
    
    
    
    