import os
import argparse
import torch
import torch.nn.functional as F
from torch.backends import cudnn
from model import Generator, Discriminator
from data_loader import get_loader
from torch.utils.tensorboard import SummaryWriter
import datetime
import time
import json
from torchvision.utils import save_image, make_grid

def str2bool(v):
    return v.lower() in ('true')

def label2onehot(labels, dim):
    """Convert label indices to one-hot vectors."""
    batch_size = labels.size(0)
    out = torch.zeros(batch_size, dim)
    out[torch.arange(batch_size), labels.long()] = 1
    return out

class Finetuner(object):
    """Finetuner for AttentionGAN."""
    
    def __init__(self, data_loader, config):
        """Initialize configurations."""
        
        # Data loader
        self.data_loader = data_loader
        
        # Model configurations
        self.c_dim = config.c_dim
        self.image_size = config.image_size
        self.g_conv_dim = config.g_conv_dim
        self.d_conv_dim = config.d_conv_dim
        self.g_repeat_num = config.g_repeat_num
        self.d_repeat_num = config.d_repeat_num
        self.lambda_cls = config.lambda_cls
        self.lambda_rec = config.lambda_rec
        self.lambda_gp = config.lambda_gp
        
        # Training configurations
        self.dataset = 'CelebA'  # Hard-coded for fine-tuning
        self.batch_size = config.batch_size
        self.num_iters = config.num_iters
        self.num_iters_decay = config.num_iters_decay
        self.g_lr = config.g_lr
        self.d_lr = config.d_lr
        self.n_critic = config.n_critic
        self.beta1 = config.beta1
        self.beta2 = config.beta2
        self.pretrained_model = config.pretrained_model
        self.selected_attrs = config.selected_attrs
        self.resume_iters = config.resume_iters if hasattr(config, 'resume_iters') else 0
        
        # Miscellaneous
        self.device = torch.device('cuda' if torch.cuda.is_available() and config.gpu else 'cpu')
        
        # Directories
        self.log_dir = config.log_dir
        self.sample_dir = config.sample_dir
        self.model_save_dir = config.model_save_dir
        self.result_dir = config.result_dir
        
        # Step sizes
        self.log_step = config.log_step
        self.sample_step = config.sample_step
        self.model_save_step = config.model_save_step
        self.lr_update_step = config.lr_update_step
        
        # Build the model
        self.build_model()
        
        # Build tensorboard
        self.writer = SummaryWriter(log_dir=self.log_dir)
    
    def build_model(self):
        """Create a generator and a discriminator."""
        self.G = Generator(self.g_conv_dim, self.c_dim, self.g_repeat_num)
        self.D = Discriminator(self.image_size, self.d_conv_dim, self.c_dim, self.d_repeat_num)
        
        self.print_network(self.G, 'G')
        self.print_network(self.D, 'D')
        
        # Optimizers with different learning rates for different parts
        # This is crucial for fine-tuning
        g_encoder_params = []
        g_decoder_params = []
        
        # Separate encoder and decoder parameters for different learning rates
        for name, param in self.G.named_parameters():
            if 'encoder' in name.lower() or any(x in name.lower() for x in ['conv1', 'conv2', 'conv3']):
                g_encoder_params.append(param)
            else:
                g_decoder_params.append(param)
        
        # 提高学习率，使模型能更好地学习新数据集的特征
        self.g_optimizer = torch.optim.Adam([
            {'params': g_encoder_params, 'lr': self.g_lr * 0.05},  # 提高编码器学习率
            {'params': g_decoder_params, 'lr': self.g_lr * 0.3}    # 提高解码器学习率
        ], betas=[self.beta1, self.beta2])
        
        # 判别器也使用较高的学习率
        self.d_optimizer = torch.optim.Adam(self.D.parameters(), self.d_lr * 0.05, [self.beta1, self.beta2])
        
        # Load pretrained model - CRITICAL for fine-tuning
        self.load_pretrained_model()
        
        # Move to GPU
        self.G.to(self.device)
        self.D.to(self.device)
    
    def print_network(self, model, name):
        """Print out the network information."""
        num_params = 0
        for p in model.parameters():
            num_params += p.numel()
        print(model)
        print(name)
        print("The number of parameters: {}".format(num_params))
    
    def load_pretrained_model(self):
        """Load pretrained generator and discriminator."""
        if self.pretrained_model:
            print(f'Loading pretrained model from {self.pretrained_model}')
            G_path = self.pretrained_model
            
            # 直接指定预训练判别器的路径
            D_path = '/root/tf-logs/FOUND_code/attentiongan/checkpoints/pretrained_D/D_pretrained_10000.ckpt'
            
            # Load generator
            self.G.load_state_dict(torch.load(G_path, map_location=lambda storage, loc: storage))
            print(f'Loaded pre-trained generator from {G_path}')
            
            # Load discriminator
            if os.path.exists(D_path):
                self.D.load_state_dict(torch.load(D_path, map_location=lambda storage, loc: storage))
                print(f'Loaded pre-trained discriminator from {D_path}')
            else:
                raise ValueError(f"Pre-trained discriminator not found at {D_path}")
    
    def save_model(self, iteration):
        """Save the generator and discriminator."""
        G_path = os.path.join(self.model_save_dir, '{}-G.ckpt'.format(iteration+1))
        D_path = os.path.join(self.model_save_dir, '{}-D.ckpt'.format(iteration+1))
        torch.save(self.G.state_dict(), G_path)
        torch.save(self.D.state_dict(), D_path)
        print('Saved model checkpoints into {}...'.format(self.model_save_dir))
        
        # Also save latest model
        G_path_latest = os.path.join(self.model_save_dir, 'latest-G.ckpt')
        D_path_latest = os.path.join(self.model_save_dir, 'latest-D.ckpt') 
        torch.save(self.G.state_dict(), G_path_latest)
        torch.save(self.D.state_dict(), D_path_latest)
    
    def update_lr(self, g_lr, d_lr):
        """Decay learning rates of the generator and discriminator."""
        for i, param_group in enumerate(self.g_optimizer.param_groups):
            if i == 0:  # encoder
                param_group['lr'] = g_lr * 0.05  # 与初始设置保持一致
            else:  # decoder
                param_group['lr'] = g_lr * 0.3   # 与初始设置保持一致
        
        # 与初始设置保持一致
        for param_group in self.d_optimizer.param_groups:
            param_group['lr'] = d_lr * 0.05
    
    def reset_grad(self):
        """Reset the gradient buffers."""
        self.g_optimizer.zero_grad()
        self.d_optimizer.zero_grad()
    
    def denorm(self, x):
        """Convert the range from [-1, 1] to [0, 1]."""
        out = (x + 1) / 2
        return out.clamp_(0, 1)
    
    def gradient_penalty(self, y, x):
        """Compute gradient penalty: (L2_norm(dy/dx) - 1)**2."""
        weight = torch.ones(y.size()).to(self.device)
        dydx = torch.autograd.grad(outputs=y,
                                   inputs=x,
                                   grad_outputs=weight,
                                   retain_graph=True,
                                   create_graph=True,
                                   only_inputs=True)[0]

        dydx = dydx.view(dydx.size(0), -1)
        dydx_l2norm = torch.sqrt(torch.sum(dydx ** 2, dim=1))
        return torch.mean((dydx_l2norm - 1) ** 2)
    
    def classification_loss(self, logit, target):
        """Compute binary cross entropy loss."""
        return F.binary_cross_entropy_with_logits(logit, target, size_average=False) / logit.size(0)
    
    def create_labels(self, c_org):
        """Generate target domain labels for debugging and testing."""
        # Get hair color indices
        hair_color_indices = []
        for i, attr_name in enumerate(self.selected_attrs):
            if attr_name in ['Black_Hair', 'Blond_Hair', 'Brown_Hair', 'Gray_Hair']:
                hair_color_indices.append(i)
                
        c_trg_list = [c_org.to(self.device)]
        for i in range(self.c_dim):
            c_trg = c_org.clone()
            if i in hair_color_indices:  # Set one hair color to 1 and the rest to 0
                c_trg[:, i] = 1
                for j in hair_color_indices:
                    if j != i:
                        c_trg[:, j] = 0
            else:
                c_trg[:, i] = (c_trg[:, i] == 0)  # Reverse attribute value
                
            c_trg_list.append(c_trg.to(self.device))
        return c_trg_list
    
    def finetune(self):
        """Fine-tune AttentionGAN using the watermarked dataset."""
        # Fetch fixed inputs for debugging
        data_iter = iter(self.data_loader)
        x_fixed, c_org = next(data_iter)
        x_fixed = x_fixed.to(self.device)
        c_fixed_list = self.create_labels(c_org)
        
        # Learning rate cache for decaying
        g_lr = self.g_lr
        d_lr = self.d_lr
        
        # Start fine-tuning
        print('Start fine-tuning from iteration {}...'.format(self.resume_iters))
        start_time = time.time()
        
        for i in range(self.resume_iters, self.num_iters):
            # =================================================================================== #
            #                             1. Preprocess input data                                #
            # =================================================================================== #
            
            # Fetch real images and labels
            try:
                x_real, label_org = next(data_iter)
            except:
                data_iter = iter(self.data_loader)
                x_real, label_org = next(data_iter)
            
            # Generate target domain labels randomly
            rand_idx = torch.randperm(label_org.size(0))
            label_trg = label_org[rand_idx]
            
            c_org = label_org.clone()
            c_trg = label_trg.clone()
            
            x_real = x_real.to(self.device)
            c_org = c_org.to(self.device)
            c_trg = c_trg.to(self.device)
            label_org = label_org.to(self.device)
            label_trg = label_trg.to(self.device)
            
            # =================================================================================== #
            #                             2. Train the discriminator                              #
            # =================================================================================== #
            
            # Compute loss with real images
            out_src, out_cls = self.D(x_real)
            d_loss_real = -torch.mean(out_src)
            d_loss_cls = self.classification_loss(out_cls, label_org)
            
            # Compute loss with fake images
            x_fake, attention_mask, content_mask, _ = self.G(x_real, c_trg)
            out_src, out_cls = self.D(x_fake.detach())
            d_loss_fake = torch.mean(out_src)
            
            # Compute loss for gradient penalty
            alpha = torch.rand(x_real.size(0), 1, 1, 1).to(self.device)
            x_hat = (alpha * x_real.data + (1 - alpha) * x_fake.data).requires_grad_(True)
            out_src, _ = self.D(x_hat)
            d_loss_gp = self.gradient_penalty(out_src, x_hat)
            
            # Backward and optimize
            d_loss = d_loss_real + d_loss_fake + self.lambda_cls * d_loss_cls + self.lambda_gp * d_loss_gp
            self.reset_grad()
            d_loss.backward()
            self.d_optimizer.step()
            
            # Logging
            loss = {}
            loss['D/loss_real'] = d_loss_real.item()
            loss['D/loss_fake'] = d_loss_fake.item()
            loss['D/loss_cls'] = d_loss_cls.item()
            loss['D/loss_gp'] = d_loss_gp.item()
            
            # =================================================================================== #
            #                               3. Train the generator                                #
            # =================================================================================== #
            
            if (i + 1) % self.n_critic == 0:
                # Original-to-target domain
                x_fake, attention_mask, content_mask, _ = self.G(x_real, c_trg)
                out_src, out_cls = self.D(x_fake)
                g_loss_fake = -torch.mean(out_src)
                g_loss_cls = self.classification_loss(out_cls, label_trg)
                
                # Target-to-original domain
                x_reconst, _, _, _ = self.G(x_fake, c_org)
                g_loss_rec = torch.mean(torch.abs(x_real - x_reconst))
                
                # Backward and optimize
                g_loss = g_loss_fake + self.lambda_rec * g_loss_rec + self.lambda_cls * g_loss_cls
                self.reset_grad()
                g_loss.backward()
                self.g_optimizer.step()
                
                # Logging
                loss['G/loss_fake'] = g_loss_fake.item()
                loss['G/loss_rec'] = g_loss_rec.item()
                loss['G/loss_cls'] = g_loss_cls.item()
                
                # Generate sample images for debugging
                if (i + 1) % self.sample_step == 0:
                    with torch.no_grad():
                        x_fake_list = [x_fixed]
                        for c_fixed in c_fixed_list[1:]:  # Skip the first one (original domain)
                            fake_image, _, _, _ = self.G(x_fixed, c_fixed)
                            x_fake_list.append(fake_image)
                        x_concat = torch.cat(x_fake_list, dim=3)
                        sample_path = os.path.join(self.sample_dir, '{}-images.jpg'.format(i+1))
                        save_image(self.denorm(x_concat.data.cpu()), sample_path, nrow=1, padding=0)
                        print('Saved sample images into {}...'.format(sample_path))
                        
                        # Save to tensorboard - 修复形状不匹配问题
                        # 将连接后的单个大图像作为一个图像添加到TensorBoard
                        # 需要手动将batch维度压缩为一个样本
                        grid = self.denorm(x_concat.data.cpu())
                        if grid.dim() == 4 and grid.size(0) > 1:
                            # 如果有多个样本，只取第一个
                            grid = grid[0].unsqueeze(0)  # [1,C,H,W]
                            
                        # 使用make_grid将多张图像拼成一张网格图
                        grid_image = make_grid(grid, nrow=1, padding=0, normalize=False)
                        self.writer.add_image('Sample', grid_image, i+1)
            
            # =================================================================================== #
            #                                 4. Miscellaneous                                    #
            # =================================================================================== #
            
            # Print out training information
            if (i + 1) % self.log_step == 0:
                et = time.time() - start_time
                et = str(datetime.timedelta(seconds=et))[:-7]
                log = "Elapsed [{}], Iteration [{}/{}]".format(et, i+1, self.num_iters)
                
                # Print losses
                for tag, value in loss.items():
                    log += ", {}: {:.4f}".format(tag, value)
                    self.writer.add_scalar(tag, value, i+1)
                print(log)
            
            # Save model checkpoints
            if (i + 1) % self.model_save_step == 0:
                self.save_model(i)
            
            # Decay learning rates
            if (i + 1) % self.lr_update_step == 0 and (i + 1) > (self.num_iters - self.num_iters_decay):
                g_lr = g_lr - (self.g_lr / float(self.num_iters_decay))
                d_lr = d_lr - (self.d_lr / float(self.num_iters_decay))
                self.update_lr(g_lr, d_lr)
                print('Decayed learning rates, g_lr: {}, d_lr: {}.'.format(g_lr, d_lr))

def main(config):
    # For fast training
    cudnn.benchmark = True
    
    # Create directories
    if not os.path.exists(config.log_dir):
        os.makedirs(config.log_dir)
    if not os.path.exists(config.model_save_dir):
        os.makedirs(config.model_save_dir)
    if not os.path.exists(config.sample_dir):
        os.makedirs(config.sample_dir)
    if not os.path.exists(config.result_dir):
        os.makedirs(config.result_dir)
    
    # Save config
    with open(os.path.join(config.model_save_dir, 'config.json'), 'w') as f:
        json.dump(vars(config), f, indent=4)
    
    # Data loader
    data_loader = get_loader(config.image_dir, config.attr_path, config.selected_attrs,
                            config.celeba_crop_size, config.image_size, config.batch_size,
                            'CelebA', config.mode, config.num_workers)
    
    # Finetuner
    finetuner = Finetuner(data_loader, config)
    finetuner.finetune()

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    
    # Model configuration
    parser.add_argument('--c_dim', type=int, default=5, help='dimension of domain labels')
    parser.add_argument('--image_size', type=int, default=256, help='image resolution')
    parser.add_argument('--g_conv_dim', type=int, default=64, help='number of conv filters in the first layer of G')
    parser.add_argument('--d_conv_dim', type=int, default=64, help='number of conv filters in the first layer of D')
    parser.add_argument('--g_repeat_num', type=int, default=6, help='number of residual blocks in G')
    parser.add_argument('--d_repeat_num', type=int, default=6, help='number of strided conv layers in D')
    parser.add_argument('--lambda_cls', type=float, default=1, help='weight for domain classification loss')
    parser.add_argument('--lambda_rec', type=float, default=25, help='weight for reconstruction loss')
    parser.add_argument('--lambda_gp', type=float, default=10, help='weight for gradient penalty')
    
    # Training configuration
    parser.add_argument('--batch_size', type=int, default=16, help='mini-batch size')
    parser.add_argument('--num_iters', type=int, default=10000, help='number of total iterations for fine-tuning')
    parser.add_argument('--num_iters_decay', type=int, default=500, help='number of iterations for decaying lr')
    parser.add_argument('--g_lr', type=float, default=0.003, help='learning rate for G')
    parser.add_argument('--d_lr', type=float, default=0.003, help='learning rate for D')
    parser.add_argument('--n_critic', type=int, default=1, help='number of D updates per each G update')
    parser.add_argument('--beta1', type=float, default=0.5, help='beta1 for Adam optimizer')
    parser.add_argument('--beta2', type=float, default=0.999, help='beta2 for Adam optimizer')
    parser.add_argument('--pretrained_model', type=str, required=True, help='path to the pretrained generator model')
    parser.add_argument('--resume_iters', type=int, default=0, help='resume training from this step')
    
    # Miscellaneous
    parser.add_argument('--num_workers', type=int, default=1)
    parser.add_argument('--mode', type=str, default='train', choices=['train', 'test'])
    parser.add_argument('--gpu', type=str2bool, default=True)
    
    # Directories
    parser.add_argument('--image_dir', type=str, required=True, help='directory containing the watermarked images')
    parser.add_argument('--attr_path', type=str, required=True, help='path to the attribute list file')
    parser.add_argument('--log_dir', type=str, default='attentiongan/logs')
    parser.add_argument('--model_save_dir', type=str, required=True, help='directory to save the checkpoints')
    parser.add_argument('--sample_dir', type=str, default='attentiongan/samples')
    parser.add_argument('--result_dir', type=str, default='attentiongan/results')
    
    # Step size
    parser.add_argument('--log_step', type=int, default=10)
    parser.add_argument('--sample_step', type=int, default=500)
    parser.add_argument('--model_save_step', type=int, default=100)
    parser.add_argument('--lr_update_step', type=int, default=500)
    
    # Dataset configuration
    parser.add_argument('--celeba_crop_size', type=int, default=178, help='crop size for the CelebA dataset')
    parser.add_argument('--selected_attrs', '--list', nargs='+', help='selected attributes for the CelebA dataset',
                        default=['Black_Hair', 'Blond_Hair', 'Brown_Hair', 'Male', 'Young'])
    
    config = parser.parse_args()
    print(config)
    main(config)


# python finetune.py \
#     --pretrained_model /root/tf-logs/FOUND_code/attentiongan/checkpoints/celeba_256_pretrained/200000-G.ckpt \
#     --image_dir /root/autodl-tmp/Celeba_watermarket_FOUND \
#     --attr_path /root/autodl-tmp/Celeba_watermarket_FOUND.txt \
#     --model_save_dir ./attentiongan/checkpoints/finetune \
#     --selected_attrs Black_Hair Blond_Hair Brown_Hair Male Young \
#     --batch_size 16 \
#     --num_iters 10000 \
#     --gpu True