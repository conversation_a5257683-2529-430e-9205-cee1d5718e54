import os
import argparse
import torch
import torch.nn.functional as F
from torch.backends import cudnn
from model import Generator, Discriminator
from data_loader import get_loader
import time
import datetime

def compute_gradient_penalty(d_out, x_in):
    """计算梯度惩罚"""
    batch_size = x_in.size(0)
    grad_dout = torch.autograd.grad(
        outputs=d_out.sum(), inputs=x_in,
        create_graph=True, retain_graph=True, only_inputs=True
    )[0]
    grad_dout2 = grad_dout.pow(2)
    assert(grad_dout2.size() == x_in.size())
    reg = grad_dout2.view(batch_size, -1).sum(1)
    return reg.mean()

class DiscriminatorTrainer(object):
    def __init__(self, data_loader, config):
        """初始化训练器"""
        self.data_loader = data_loader
        self.device = torch.device('cuda' if torch.cuda.is_available() and config.gpu else 'cpu')
        
        # 创建生成器和判别器
        self.G = Generator(config.g_conv_dim, config.c_dim, config.g_repeat_num)
        self.D = Discriminator(config.image_size, config.d_conv_dim, config.c_dim, config.d_repeat_num)
        
        # 加载预训练的生成器
        self.G.load_state_dict(torch.load(config.pretrained_model, map_location=lambda storage, loc: storage))
        print(f'Loaded pretrained generator from {config.pretrained_model}')
        
        # 移动模型到设备
        self.G = self.G.to(self.device)
        self.D = self.D.to(self.device)
        
        # 优化器
        self.d_optimizer = torch.optim.Adam(self.D.parameters(), config.d_lr, [config.beta1, config.beta2])
        
        # 其他配置
        self.config = config
        self.batch_size = config.batch_size
        self.pretrain_steps = config.pretrain_steps
        self.lambda_cls = config.lambda_cls
        self.lambda_gp = config.lambda_gp
        
    def train(self):
        """训练判别器"""
        # 数据迭代器
        data_iter = iter(self.data_loader)
        
        # 开始计时
        start_time = time.time()
        print('Start training discriminator...')
        
        # 设置模型模式
        self.G.eval()  # 生成器设置为评估模式
        self.D.train() # 判别器设置为训练模式
        
        for i in range(self.pretrain_steps):
            # =================================================================================== #
            #                             1. 准备输入数据                                          #
            # =================================================================================== #
            try:
                x_real, label_org = next(data_iter)
            except:
                data_iter = iter(self.data_loader)
                x_real, label_org = next(data_iter)
                
            # 移动数据到设备
            x_real = x_real.to(self.device)
            label_org = label_org.to(self.device)
            
            # =================================================================================== #
            #                             2. 训练判别器                                           #
            # =================================================================================== #
            
            # 计算真实图片的判别器损失
            out_src, out_cls = self.D(x_real)
            d_loss_real = -torch.mean(out_src)
            d_loss_cls = self.lambda_cls * F.binary_cross_entropy_with_logits(out_cls, label_org)
            
            # 用预训练的生成器生成假图片
            with torch.no_grad():
                x_fake, _, _, _ = self.G(x_real, label_org)
            
            # 计算假图片的判别器损失
            out_src, _ = self.D(x_fake.detach())
            d_loss_fake = torch.mean(out_src)
            
            # 计算梯度惩罚
            alpha = torch.rand(x_real.size(0), 1, 1, 1).to(self.device)
            x_hat = (alpha * x_real.data + (1 - alpha) * x_fake.data).requires_grad_(True)
            out_src, _ = self.D(x_hat)
            d_loss_gp = self.lambda_gp * compute_gradient_penalty(out_src, x_hat)
            
            # 总损失
            d_loss = d_loss_real + d_loss_fake + d_loss_cls + d_loss_gp
            
            # 优化判别器
            self.d_optimizer.zero_grad()
            d_loss.backward()
            self.d_optimizer.step()
            
            # =================================================================================== #
            #                             3. 记录和打印                                           #
            # =================================================================================== #
            
            # 打印训练信息
            if (i+1) % self.config.log_step == 0:
                et = time.time() - start_time
                et = str(datetime.timedelta(seconds=et))[:-7]
                print(f'Elapsed [{et}], Step [{i+1}/{self.pretrain_steps}], '
                      f'D/loss: {d_loss.item():.4f}, '
                      f'D/loss_real: {d_loss_real.item():.4f}, '
                      f'D/loss_fake: {d_loss_fake.item():.4f}, '
                      f'D/loss_cls: {d_loss_cls.item():.4f}, '
                      f'D/loss_gp: {d_loss_gp.item():.4f}')
            
            # 保存模型
            if (i+1) % self.config.model_save_step == 0:
                D_path = os.path.join(self.config.model_save_dir, f'D_pretrained_{i+1}.ckpt')
                torch.save(self.D.state_dict(), D_path)
                print(f'Saved model checkpoint to {D_path}')

def main(config):
    # 为快速训练
    cudnn.benchmark = True
    
    # 创建目录
    os.makedirs(config.model_save_dir, exist_ok=True)
    
    # 数据加载器
    data_loader = get_loader(config.celeba_image_dir, config.attr_path, config.selected_attrs,
                           config.celeba_crop_size, config.image_size, config.batch_size,
                           'CelebA', config.mode, config.num_workers)
    
    # 创建训练器
    trainer = DiscriminatorTrainer(data_loader, config)
    
    # 开始训练
    trainer.train()

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    
    # 模型配置
    parser.add_argument('--c_dim', type=int, default=5, help='dimension of domain labels')
    parser.add_argument('--image_size', type=int, default=256, help='image resolution')
    parser.add_argument('--g_conv_dim', type=int, default=64, help='number of conv filters in the first layer of G')
    parser.add_argument('--d_conv_dim', type=int, default=64, help='number of conv filters in the first layer of D')
    parser.add_argument('--g_repeat_num', type=int, default=6, help='number of residual blocks in G')
    parser.add_argument('--d_repeat_num', type=int, default=6, help='number of strided conv layers in D')
    parser.add_argument('--lambda_cls', type=float, default=1, help='weight for domain classification loss')
    parser.add_argument('--lambda_gp', type=float, default=10, help='weight for gradient penalty')
    
    # 训练配置
    parser.add_argument('--batch_size', type=int, default=16, help='mini-batch size')
    parser.add_argument('--pretrain_steps', type=int, default=10000, help='number of total iterations for pretraining D')
    parser.add_argument('--d_lr', type=float, default=0.0002, help='learning rate for D')
    parser.add_argument('--beta1', type=float, default=0.5, help='beta1 for Adam optimizer')
    parser.add_argument('--beta2', type=float, default=0.999, help='beta2 for Adam optimizer')
    parser.add_argument('--gpu', type=bool, default=True, help='use gpu or not')
    
    # 路径配置
    parser.add_argument('--celeba_image_dir', type=str, required=True, help='directory containing the original CelebA images')
    parser.add_argument('--attr_path', type=str, required=True, help='path to the CelebA attribute list file')
    parser.add_argument('--pretrained_model', type=str, required=True, help='path to the pretrained generator model')
    parser.add_argument('--model_save_dir', type=str, required=True, help='directory to save the pretrained discriminator')
    
    # 步长配置
    parser.add_argument('--log_step', type=int, default=10)
    parser.add_argument('--model_save_step', type=int, default=1000)
    
    # 数据集配置
    parser.add_argument('--mode', type=str, default='train', choices=['train', 'test'])
    parser.add_argument('--num_workers', type=int, default=1)
    parser.add_argument('--celeba_crop_size', type=int, default=178, help='crop size for the CelebA dataset')
    parser.add_argument('--selected_attrs', '--list', nargs='+', help='selected attributes for the CelebA dataset',
                        default=['Black_Hair', 'Blond_Hair', 'Brown_Hair', 'Male', 'Young'])
    
    config = parser.parse_args()
    print(config)
    main(config)
    
    
# python pretrain_discriminator.py \
#     --celeba_image_dir /root/autodl-tmp/img_align_celeba \
#     --attr_path /root/tf-logs/list_attr_celeba.txt \
#     --pretrained_model /root/tf-logs/FOUND_code/attentiongan/checkpoints/celeba_256_pretrained/200000-G.ckpt \
#     --model_save_dir /root/tf-logs/FOUND_code/attentiongan/checkpoints/pretrained_D \
#     --pretrain_steps 10000 \
#     --batch_size 16 \
#     --d_lr 0.0002 \
#     --gpu 1