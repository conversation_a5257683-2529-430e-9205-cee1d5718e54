{"attrs": ["Bald", "<PERSON><PERSON>", "Black_Hair", "Blond_Hair", "<PERSON><PERSON><PERSON>", "Bushy_Eyebrows", "Eyeglasses", "Male", "Mouth_Slightly_Open", "Mustache", "No_Beard", "Pale_Skin", "<PERSON>"], "data": "CelebA", "data_path": "/root/autodl-tmp/img_align_celeba_watermarked_cropped_s3fd", "attr_path": "/root/autodl-tmp/list_attr_celeba_watermarked_cropped_s3fd.txt", "image_list_path": "data/new_dataset_path/image_list.txt", "img_size": 256, "shortcut_layers": 1, "inject_layers": 0, "enc_dim": 64, "dec_dim": 64, "dis_dim": 64, "dis_fc_dim": 1024, "enc_layers": 5, "dec_layers": 5, "dis_layers": 5, "enc_norm": "batchnorm", "dec_norm": "batchnorm", "dis_norm": "instancenorm", "dis_fc_norm": "none", "enc_acti": "lrelu", "dec_acti": "relu", "dis_acti": "lrelu", "dis_fc_acti": "relu", "lambda_1": 100.0, "lambda_2": 10.0, "lambda_3": 1.0, "lambda_gp": 10.0, "mode": "wgan", "epochs": 100, "batch_size": 32, "num_workers": 0, "lr": 0.0002, "beta1": 0.5, "beta2": 0.999, "n_d": 5, "b_distribution": "none", "thres_int": 0.5, "test_int": 1.0, "n_samples": 16, "load_checkpoint_path": "/root/tf-logs/FOUND_code/attgan/output/256_shortcut1_inject0_none/checkpoint/weights.199.pth", "save_interval": 1000, "sample_interval": 1000, "gpu": true, "multi_gpu": false, "experiment_name": "256_shortcut1_inject0_none_finetune111", "lr_base": 0.0002, "n_attrs": 13, "betas": [0.5, 0.999]}