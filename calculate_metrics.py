import os
import argparse
import numpy as np
from PIL import Image
from skimage.metrics import structural_similarity
from tqdm import tqdm
import torch

# 导入 pytorch-fid 库的核心函数
# 如果导入失败，说明库未安装
try:
    from pytorch_fid import fid_score
except ImportError:
    print("错误: 无法导入 pytorch_fid。")
    print("请先安装必要的库: pip install numpy scikit-image Pillow torch torchvision pytorch-fid")
    exit()

def calculate_ssim_for_pair(img1_path, img2_path):
    """
    计算单对图像的SSIM值。
    使用您指定的参数。
    """
    try:
        # 加载图像并转换为 NumPy 数组
        img1 = Image.open(img1_path).convert('RGB')
        img2 = Image.open(img2_path).convert('RGB')
        
        # 确保图像尺寸一致，如果不一致则将第二张图缩放到与第一张相同
        if img1.size != img2.size:
            img2 = img2.resize(img1.size)
            
        img1_np = np.array(img1)
        img2_np = np.array(img2)
        
        # 计算SSIM，使用您提供的参数
        ssim_value = structural_similarity(
            img1_np, 
            img2_np, 
            multichannel=True, 
            win_size=3, 
            data_range=img1_np.max() - img1_np.min()
        )
        return ssim_value
    except Exception as e:
        print(f"\n计算SSIM时出错 (图像: {os.path.basename(img1_path)}): {e}")
        return None

def run_comparison(folder1, folder2):
    """
    主函数，执行两个文件夹中图像的SSIM和FID比较。
    """
    print(f"开始比较以下两个文件夹：")
    print(f"文件夹 A: {folder1}")
    print(f"文件夹 B: {folder2}")
    
    # --- 1. 查找匹配的文件 ---
    try:
        files1 = set(os.listdir(folder1))
        files2 = set(os.listdir(folder2))
    except FileNotFoundError as e:
        print(f"\n错误：找不到文件夹: {e.filename}")
        return

    common_files = sorted(list(files1.intersection(files2)))
    
    # 过滤掉非图像文件（可选，但推荐）
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff'}
    common_images = [
        f for f in common_files 
        if os.path.splitext(f)[1].lower() in image_extensions
    ]

    if not common_images:
        print("\n错误：在两个文件夹中没有找到任何同名的图像文件。请检查您的文件夹路径和内容。")
        return
        
    print(f"\n共找到 {len(common_images)} 个同名图像文件，将开始计算...")

    # --- 2. 逐对计算 SSIM ---
    ssim_scores = []
    print("\n--- 开始计算 SSIM (逐对) ---")
    for filename in tqdm(common_images, desc="计算SSIM"):
        path1 = os.path.join(folder1, filename)
        path2 = os.path.join(folder2, filename)
        
        ssim_value = calculate_ssim_for_pair(path1, path2)
        
        if ssim_value is not None:
            print(f"  SSIM for [{filename}]: {ssim_value:.4f}")
            ssim_scores.append(ssim_value)

    if ssim_scores:
        avg_ssim = np.mean(ssim_scores)
        print(f"\n所有图像对的平均 SSIM: {avg_ssim:.4f}")
    else:
        print("\n未能成功计算任何图像对的SSIM值。")

    # --- 3. 计算整体 FID ---
    # 注意：FID是衡量两个图像分布（集合）之间的距离，因此它是对整个文件夹计算一次，而不是逐对计算。
    print("\n--- 开始计算 FID (整个文件夹之间) ---")
    print("这可能需要一些时间，取决于图像数量和您的硬件...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"FID计算将使用设备: {device}")
    
    try:
        # 使用 fid_score.calculate_fid_given_paths 函数计算 FID
        # 它需要一个路径列表，一个批处理大小，设备和要使用的Inception层维度
        fid_value = fid_score.calculate_fid_given_paths(
            paths=[folder1, folder2],
            batch_size=50,
            device=device,
            dims=2048 # 这是标准的FID计算维度
        )
        print(f"\n两个文件夹之间的 FID 分数: {fid_value:.4f}")
    except Exception as e:
        print(f"\n计算FID时发生错误: {e}")
        print("请确保两个文件夹中都有足够数量的有效图像。")
        
def main():
    parser = argparse.ArgumentParser(
        description="计算两个文件夹中同名图像的SSIM和FID分数。",
        formatter_class=argparse.RawTextHelpFormatter # 更好的格式化帮助信息
    )
    parser.add_argument(
        '--folder1', 
        type=str, 
        required=True, 
        help="第一个文件夹的路径 (例如，包含原始图像或第一组伪造图)。"
    )
    parser.add_argument(
        '--folder2', 
        type=str, 
        required=True, 
        help="第二个文件夹的路径 (例如，包含带水印的伪造图或第二组伪造图)。"
    )
    
    args = parser.parse_args()
    
    run_comparison(args.folder1, args.folder2)

if __name__ == '__main__':
    main()
    
    
# python calculate_metrics.py --folder1 /root/tf-logs/FOUND_code/shiyan_original/originals --folder2 /root/tf-logs/FOUND_code/shiyan/AttGAN_fakes