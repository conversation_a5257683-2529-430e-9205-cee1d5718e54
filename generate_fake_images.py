import argparse
import torch
import os
from os.path import join
import torchvision.utils as vutils
from PIL import Image
import torchvision.transforms as transforms
from model_data_prepare import prepare
from attgan.data import check_attribute_conflict
import numpy as np
import random
from pathlib import Path

os.environ['CUDA_VISIBLE_DEVICES'] = '0'

def load_image(image_path):
    """加载并预处理输入图像"""
    transform = transforms.Compose([
        transforms.Resize((256, 256)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
    ])
    
    image = Image.open(image_path).convert('RGB')
    image = transform(image).unsqueeze(0)
    return image

def load_watermark(watermark_path):
    """加载预训练的水印"""
    watermark = torch.load(watermark_path)
    return watermark

def get_random_images(image_dir, num_images=5):
    """从图像目录中随机选择指定数量的图像"""
    valid_extensions = {'.jpg', '.jpeg', '.png', '.bmp'}
    image_files = [
        f for f in Path(image_dir).iterdir()
        if f.is_file() and f.suffix.lower() in valid_extensions
    ]
    
    if len(image_files) < num_images:
        raise ValueError(f"目录中的图像数量({len(image_files)})少于请求的数量({num_images})")
    
    selected_images = random.sample(image_files, num_images)
    return selected_images

def perform_stargan(image, stargan_solver, c_org):
    """处理StarGAN模型的图像生成"""
    c_trg = torch.zeros_like(c_org)
    c_trg[:, np.random.randint(0, stargan_solver.c_dim)] = 1
    
    with torch.no_grad():
        x_fake = stargan_solver.G(image, c_trg)
    return x_fake

def perform_attentiongan(image, attentiongan_solver, c_org):
    """处理AttentionGAN模型的图像生成"""
    c_trg = torch.zeros_like(c_org)
    c_trg[:, np.random.randint(0, attentiongan_solver.c_dim)] = 1
    
    with torch.no_grad():
        x_fake = attentiongan_solver.G(image, c_trg)
    return x_fake

def generate_fake_images(image_dir, watermark_path, output_dir, num_images=5):
    """为多张输入图像生成伪造图像并保存"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    os.makedirs(output_dir, exist_ok=True)
    
    stargan_collection_dir = join(output_dir, 'all_stargan_fakes')
    attgan_collection_dir = join(output_dir, 'all_attgan_fakes')
    os.makedirs(stargan_collection_dir, exist_ok=True)
    os.makedirs(attgan_collection_dir, exist_ok=True)

    selected_images = get_random_images(image_dir, num_images)
    
    watermark = load_watermark(watermark_path).to(device)
    
    _, _, attgan, attgan_args, stargan_solver, attentiongan_solver, transform, F, T, G, E, reference, gen_models = prepare()
    
    for idx, image_path in enumerate(selected_images):
        print(f"处理图像 {idx+1}/{num_images}: {image_path.name}")
        
        image_output_dir = join(output_dir, f"image_{idx+1}")
        os.makedirs(image_output_dir, exist_ok=True)
        
        image = load_image(str(image_path)).to(device)
        
        att_a = torch.zeros(1, attgan_args.n_attrs).to(device)
        att_b_list = []
        for i in range(attgan_args.n_attrs):
            tmp = att_a.clone()
            tmp[:, i] = 1
            tmp = check_attribute_conflict(tmp, attgan_args.attrs[i], attgan_args.attrs)
            att_b_list.append((tmp, attgan_args.attrs[i]))
        
        selected_attrs = np.random.choice(len(att_b_list), 4, replace=False)
        
        c_org_stargan = torch.zeros(1, stargan_solver.c_dim).to(device)
        c_org_attention = torch.zeros(1, attentiongan_solver.c_dim).to(device)
        
        with torch.no_grad():
            fake_images_clean = []
            
            c = E(image)
            s_trg = F(reference, 1)
            c_trg = T(c, s_trg, 1)
            fake_hidf = G(c_trg)
            fake_images_clean.append(('HiDF', fake_hidf))
            
            att_b, attr_name = att_b_list[selected_attrs[0]]
            att_b_ = (att_b * 2 - 1) * attgan_args.thres_int
            fake_attgan, _ = attgan.G(image, att_b_)
            fake_images_clean.append(('AttGAN_' + attr_name, fake_attgan))
            
            fake_stargan = perform_stargan(image, stargan_solver, c_org_stargan)
            fake_images_clean.append(('StarGAN', fake_stargan))
            
            fake_attentiongan = perform_attentiongan(image, attentiongan_solver, c_org_attention)
            fake_images_clean.append(('AttentionGAN', fake_attentiongan))

            # =============================================================================
            #  核心修正区域: 在保存前，检查变量是否为元组
            # =============================================================================
            original_stem = image_path.stem
            
            # --- 修正StarGAN保存逻辑 ---
            # 1. 检查fake_stargan是否为元组，如果是，则只取第一个元素（图像张量）
            stargan_img_to_save = fake_stargan[0] if isinstance(fake_stargan, tuple) else fake_stargan
            # 2. 保存处理后的图像张量
            stargan_save_path = join(stargan_collection_dir, f"{original_stem}_stargan.png")
            vutils.save_image(stargan_img_to_save, stargan_save_path, normalize=True, value_range=(-1., 1.))
            
            # --- 修正AttGAN保存逻辑 (同样增加安全检查) ---
            # 注：根据 `fake_attgan, _ = attgan.G(...)` 的写法，fake_attgan本身已是张量。
            # 但为保持代码健壮性，加上检查无害。
            attgan_img_to_save = fake_attgan[0] if isinstance(fake_attgan, tuple) else fake_attgan
            attgan_save_path = join(attgan_collection_dir, f"{original_stem}_attgan_{attr_name}.png")
            vutils.save_image(attgan_img_to_save, attgan_save_path, normalize=True, value_range=(-1., 1.))
            # =============================================================================

            watermarked_image = image + watermark
            watermarked_image = torch.clamp(watermarked_image, -1, 1)
            fake_images_watermarked = []
            
            c = E(watermarked_image)
            s_trg = F(reference, 1)
            c_trg = T(c, s_trg, 1)
            fake_hidf_w = G(c_trg)
            fake_images_watermarked.append(('HiDF_watermarked', fake_hidf_w))
            
            fake_attgan_w, _ = attgan.G(watermarked_image, att_b_)
            fake_images_watermarked.append(('AttGAN_watermarked_' + attr_name, fake_attgan_w))
            
            fake_stargan_w = perform_stargan(watermarked_image, stargan_solver, c_org_stargan)
            fake_images_watermarked.append(('StarGAN_watermarked', fake_stargan_w))
            
            fake_attentiongan_w = perform_attentiongan(watermarked_image, attentiongan_solver, c_org_attention)
            fake_images_watermarked.append(('AttentionGAN_watermarked', fake_attentiongan_w))
            
            vutils.save_image(image, join(image_output_dir, 'original.png'), normalize=True, value_range=(-1., 1.))
            
            vutils.save_image(watermarked_image, join(image_output_dir, 'watermarked.png'), normalize=True, value_range=(-1., 1.))
            
            for name, fake_img in fake_images_clean:
                if isinstance(fake_img, tuple):
                    fake_img = fake_img[0]
                vutils.save_image(fake_img, join(image_output_dir, f'fake_{name}.png'), normalize=True, value_range=(-1., 1.))
            
            for name, fake_img in fake_images_watermarked:
                if isinstance(fake_img, tuple):
                    fake_img = fake_img[0]
                vutils.save_image(fake_img, join(image_output_dir, f'fake_{name}.png'), normalize=True, value_range=(-1., 1.))

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--image_dir', type=str, required=True, help='输入图像目录的路径')
    parser.add_argument('--watermark_path', type=str, required=True, help='水印文件的路径')
    parser.add_argument('--output_dir', type=str, default='output_fake_images', help='输出目录的路径')
    parser.add_argument('--num_images', type=int, default=5, help='要处理的图像数量')
    args = parser.parse_args()
    
    generate_fake_images(args.image_dir, args.watermark_path, args.output_dir, args.num_images)
    print(f'所有图像已保存到 {args.output_dir} 目录')
    print(f'所有StarGAN伪造图已集中保存到 {join(args.output_dir, "all_stargan_fakes")}')
    print(f'所有AttGAN伪造图已集中保存到 {join(args.output_dir, "all_attgan_fakes")}')

if __name__ == '__main__':
    main()


# python generate_fake_images.py --image_dir /root/autodl-tmp/img_align_celeba --watermark_path /root/tf-logs/FOUND_code/pert_FOUND333.pt --output_dir /root/tf-logs/FOUND_code/shiyan --num_images 5
#python generate_fake_images.py --image_dir /home/<USER>/zhenghongrui/FOUND-main/FOUND_code/output88/11  --watermark_path /home/<USER>/zhenghongrui/FOUND-main/FOUND_code/perturbation_CMUA.pt --output_dir /home/<USER>/zhenghongrui/FOUND-main/FOUND_code/output88 --num_images 2