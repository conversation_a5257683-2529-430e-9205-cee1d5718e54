import argparse
import torch
import os
from os.path import join
import torchvision.utils as vutils
from PIL import Image
import torchvision.transforms as transforms
from model_data_prepare import prepare
from attgan.data import check_attribute_conflict
import numpy as np
import random
from pathlib import Path

# 确保PyTorch使用的是GPU
os.environ['CUDA_VISIBLE_DEVICES'] = '0'

def load_image(image_path):
    """加载并预处理输入图像"""
    transform = transforms.Compose([
        transforms.Resize((256, 256)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
    ])
    
    image = Image.open(image_path).convert('RGB')
    image = transform(image).unsqueeze(0)
    return image

def get_random_images(image_dir, num_images=5):
    """从图像目录中随机选择指定数量的图像"""
    valid_extensions = {'.jpg', '.jpeg', '.png', '.bmp'}
    image_files = [
        f for f in Path(image_dir).iterdir()
        if f.is_file() and f.suffix.lower() in valid_extensions
    ]
    
    if len(image_files) < num_images:
        raise ValueError(f"目录中的图像数量({len(image_files)})少于请求的数量({num_images})")
    
    selected_images = random.sample(image_files, num_images)
    return selected_images

def perform_stargan(image, stargan_solver, c_org):
    """处理StarGAN模型的图像生成"""
    c_trg = torch.zeros_like(c_org)
    c_trg[:, np.random.randint(0, stargan_solver.c_dim)] = 1
    
    with torch.no_grad():
        x_fake = stargan_solver.G(image, c_trg)
    return x_fake

def perform_attentiongan(image, attentiongan_solver, c_org):
    """处理AttentionGAN模型的图像生成"""
    c_trg = torch.zeros_like(c_org)
    c_trg[:, np.random.randint(0, attentiongan_solver.c_dim)] = 1
    
    with torch.no_grad():
        x_fake = attentiongan_solver.G(image, c_trg)
    return x_fake

def save_image_tensor(tensor, path):
    """一个安全的保存函数，用于处理可能是元组的输入"""
    img_to_save = tensor[0] if isinstance(tensor, tuple) else tensor
    vutils.save_image(img_to_save, path, normalize=True, value_range=(-1., 1.))

def generate_fake_images(image_dir, output_dir, num_images=5):
    """为多张输入图像生成伪造图像并保存到分类的文件夹中"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # =============================================================================
    #  核心改动 1: 定义并创建全新的、分类清晰的输出目录结构
    # =============================================================================
    originals_dir = join(output_dir, 'originals')
    hidf_dir = join(output_dir, 'HiDF_fakes')
    attgan_dir = join(output_dir, 'AttGAN_fakes')
    stargan_dir = join(output_dir, 'StarGAN_fakes')
    attentiongan_dir = join(output_dir, 'AttentionGAN_fakes')
    
    for path in [originals_dir, hidf_dir, attgan_dir, stargan_dir, attentiongan_dir]:
        os.makedirs(path, exist_ok=True)
    # =============================================================================

    selected_images = get_random_images(image_dir, num_images)
    
    # 准备模型和数据 (不再需要加载水印)
    _, _, attgan, attgan_args, stargan_solver, attentiongan_solver, transform, F, T, G, E, reference, gen_models = prepare()
    
    for idx, image_path in enumerate(selected_images):
        print(f"处理图像 {idx+1}/{num_images}: {image_path.name}")
        
        # 加载图像
        image = load_image(str(image_path)).to(device)
        
        # 获取不带后缀的原始文件名，用于命名输出文件
        original_stem = image_path.stem
        
        # =============================================================================
        #  核心改动 2: 简化流程，移除所有水印相关逻辑，生成后立刻保存
        # =============================================================================
        
        # 1. 保存原始图像
        save_image_tensor(image, join(originals_dir, f"{original_stem}.png"))
        
        # 准备生成伪造图所需的属性向量
        att_a = torch.zeros(1, attgan_args.n_attrs).to(device)
        att_b_list = []
        for i in range(attgan_args.n_attrs):
            tmp = att_a.clone()
            tmp[:, i] = 1
            tmp = check_attribute_conflict(tmp, attgan_args.attrs[i], attgan_args.attrs)
            att_b_list.append((tmp, attgan_args.attrs[i]))
        
        selected_attr_idx = np.random.randint(0, len(att_b_list))
        
        c_org_stargan = torch.zeros(1, stargan_solver.c_dim).to(device)
        c_org_attention = torch.zeros(1, attentiongan_solver.c_dim).to(device)
        
        with torch.no_grad():
            # 2. 生成并保存HiDF伪造图
            c = E(image)
            s_trg = F(reference, 1)
            c_trg = T(c, s_trg, 1)
            fake_hidf = G(c_trg)
            save_image_tensor(fake_hidf, join(hidf_dir, f"{original_stem}_hidf.png"))
            
            # 3. 生成并保存AttGAN伪造图
            att_b, attr_name = att_b_list[selected_attr_idx]
            att_b_ = (att_b * 2 - 1) * attgan_args.thres_int
            fake_attgan, _ = attgan.G(image, att_b_)
            save_image_tensor(fake_attgan, join(attgan_dir, f"{original_stem}_attgan_{attr_name}.png"))

            # 4. 生成并保存StarGAN伪造图
            fake_stargan = perform_stargan(image, stargan_solver, c_org_stargan)
            save_image_tensor(fake_stargan, join(stargan_dir, f"{original_stem}_stargan.png"))
            
            # 5. 生成并保存AttentionGAN伪造图
            fake_attentiongan = perform_attentiongan(image, attentiongan_solver, c_org_attention)
            save_image_tensor(fake_attentiongan, join(attentiongan_dir, f"{original_stem}_attentiongan.png"))

        # =============================================================================
        #  核心改动 3: 原有的复杂保存逻辑和每个图像的子目录已完全移除
        # =============================================================================

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--image_dir', type=str, required=True, help='输入图像目录的路径')
    # --watermark_path 参数已被移除
    parser.add_argument('--output_dir', type=str, default='output_sorted_fakes', help='主输出目录的路径')
    parser.add_argument('--num_images', type=int, default=5, help='要处理的图像数量')
    args = parser.parse_args()
    
    generate_fake_images(args.image_dir, args.output_dir, args.num_images)
    
    # 更新最终的输出信息
    print(f'\n处理完成!')
    print(f'原始图像已保存到: {join(args.output_dir, "originals")}')
    print(f'HiDF伪造图已保存到: {join(args.output_dir, "HiDF_fakes")}')
    print(f'AttGAN伪造图已保存到: {join(args.output_dir, "AttGAN_fakes")}')
    print(f'StarGAN伪造图已保存到: {join(args.output_dir, "StarGAN_fakes")}')
    print(f'AttentionGAN伪造图已保存到: {join(args.output_dir, "AttentionGAN_fakes")}')

if __name__ == '__main__':
    main()
    
    
# python generate_fake_images1.py --image_dir /root/tf-logs/FOUND_code/shiyan/originals --output_dir /root/tf-logs/FOUND_code/shiyan1 --num_images 5
