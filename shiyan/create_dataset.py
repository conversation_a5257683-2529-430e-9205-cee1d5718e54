import os
import torch
import numpy as np
from tqdm import tqdm
import torchvision.transforms as transforms
from torch.utils.data import Dataset, DataLoader
from PIL import Image
import shutil

class ImageDataset(Dataset):
    def __init__(self, txt_path, img_root, transform=None):
        self.img_root = img_root
        self.transform = transform
        self.image_paths = []
        self.labels = []
        self.lines = []
        
        # 读取txt文件中的图像路径和标签
        with open(txt_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line:
                    self.lines.append(line)
                    parts = line.split()
                    if len(parts) >= 1:
                        img_path = parts[0]
                        self.image_paths.append(img_path)
                        if len(parts) > 1:
                            self.labels.append(" ".join(parts[1:]))
                        else:
                            self.labels.append("")

    def __len__(self):
        return len(self.image_paths)

    def __getitem__(self, idx):
        img_path = os.path.join(self.img_root, self.image_paths[idx])
        label = self.labels[idx]
        line = self.lines[idx]
        
        try:
            image = Image.open(img_path).convert('RGB')
            
            if self.transform:
                image = self.transform(image)
                
            return image, img_path, label, line
        except Exception as e:
            print(f"加载图像出错: {img_path}, 错误: {e}")
            # 返回一个占位图像
            return torch.zeros(3, 256, 256), img_path, label, line

def main():
    # 配置参数
    txt_path = "/root/autodl-tmp/list_attr_celeba.txt"
    img_root = "/root/autodl-tmp/img_align_celeba"
    output_dir = "/root/autodl-tmp/img_align_celeba_watermarked"
    output_txt = "/root/autodl-tmp/list_attr_celeba_watermarked.txt"
    watermark_path = "/root/tf-logs/FOUND_code/pert_FOUND444.pt"
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 确保输出txt文件的目录存在
    os.makedirs(os.path.dirname(os.path.abspath(output_txt)), exist_ok=True)
    
    # 加载水印
    watermark = torch.load(watermark_path)
    print(f"已加载水印，形状: {watermark.shape}")
    
    # 图像变换
    transform = transforms.Compose([
        transforms.Resize((256, 256)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
    ])
    
    # 创建数据集
    dataset = ImageDataset(txt_path, img_root, transform)
    
    # 获取所有图像路径、标签和原始行
    all_paths = dataset.image_paths
    all_labels = dataset.labels
    all_lines = dataset.lines
    
    # 创建索引并随机打乱
    indices = list(range(len(all_paths)))
    np.random.shuffle(indices)
    
    # 分割为干净和水印两部分
    clean_indices = indices[:1000]
    watermark_indices = indices[1000:2000]
    
    if len(watermark_indices) < 1000:
        print(f"警告：源数据集中只有 {len(all_paths)} 张图像，无法获取足够的图像进行水印处理")
        if len(all_paths) > 1000:
            watermark_indices = indices[1000:]
        else:
            # 如果图像不足20000张，则将一半用于干净图像，一半用于水印图像
            middle = len(indices) // 2
            clean_indices = indices[:middle]
            watermark_indices = indices[middle:]
    
    # 设备设置
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    watermark = watermark.to(device)
    
    # 处理并保存干净图像
    print("正在处理干净图像...")
    clean_entries = []
    for idx in tqdm(clean_indices):
        img_path = all_paths[idx]
        line = all_lines[idx]
        src_path = os.path.join(img_root, img_path)
        dst_path = os.path.join(output_dir, img_path)
        
        # 确保目标目录存在
        os.makedirs(os.path.dirname(dst_path), exist_ok=True)
        
        try:
            shutil.copy(src_path, dst_path)
            clean_entries.append(line)
        except Exception as e:
            print(f"复制图像出错: {src_path}, 错误: {e}")
    
    # 创建水印图像的数据加载器
    watermark_batch_size = 8
    watermark_dataset = torch.utils.data.Subset(dataset, watermark_indices)
    watermark_loader = DataLoader(watermark_dataset, batch_size=watermark_batch_size, shuffle=False, num_workers=4)
    
    # 处理并保存水印图像
    print("正在处理水印图像...")
    watermark_entries = []
    
    for batch in tqdm(watermark_loader):
        images, paths, labels, lines = batch
        images = images.to(device)
        
        # 确保水印形状与当前批次匹配
        batch_size = images.size(0)
        
        if watermark is None:
            raise ValueError("Watermark tensor must be provided for 'default' mode.")
        if watermark.size(0) != batch_size:
            current_watermark = watermark[0:1].repeat(batch_size, 1, 1, 1)
        else:
            current_watermark = watermark
        watermarked_images = torch.clamp(images + current_watermark, -1, 1)
        
        
        # 保存水印图像
        for i in range(batch_size):
            img = watermarked_images[i]
            path = paths[i]
            line = lines[i]
            rel_path = os.path.relpath(path, img_root)
            
            # 转换回PIL图像
            img_np = ((img + 1) / 2 * 255).byte().permute(1, 2, 0).cpu().numpy()
            img_pil = Image.fromarray(img_np)
            
            # 保存到与原始路径相同的相对路径
            dst_path = os.path.join(output_dir, rel_path)
            os.makedirs(os.path.dirname(dst_path), exist_ok=True)
            img_pil.save(dst_path)
            watermark_entries.append(line)
    
    # 创建输出txt文件
    print(f"正在创建输出txt文件: {output_txt}")
    with open(output_txt, 'w') as f:
        # 写入所有条目
        all_entries = clean_entries + watermark_entries
        for line in all_entries:
            f.write(f"{line}\n")
    
    print(f"处理完成! 已创建数据集，包含 {len(clean_entries)} 张干净图像和 {len(watermark_entries)} 张水印图像。")
    print(f"数据集标签文件已保存至: {output_txt}")

if __name__ == "__main__":
    main()