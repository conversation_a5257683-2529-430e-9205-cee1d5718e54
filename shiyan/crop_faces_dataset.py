import os
import torch
import numpy as np
from tqdm import tqdm
import torchvision.transforms as transforms
from PIL import Image
import cv2
import argparse
import sys
import time

try:
    from S3FDDetector import init_detector as init_s3fd
except ImportError:
    print("错误：无法导入 S3FDDetector。请确保您有一个 'S3FDDetector.py' 文件，"
          "其中包含一个名为 'init_detector' 的函数来加载S3FD模型。")
    sys.exit(1)


class ImageDataset:
    def __init__(self, txt_path, img_root, transform=None):
        self.img_root = img_root
        self.transform = transform
        self.image_paths = []
        self.lines = [] # 保存每一行的完整信息，包括原文件名和属性
        
        with open(txt_path, 'r') as f:
            lines = f.readlines()
            
            # 跳过头信息
            if len(lines) > 2:
                for line in lines[2:]:
                    line = line.strip()
                    if line:
                        self.lines.append(line)
                        img_path = line.split()[0]
                        self.image_paths.append(img_path)

    def __len__(self):
        return len(self.image_paths)

    def __getitem__(self, idx):
        # 使用 image_paths 和 lines 中对应的索引
        img_path = os.path.join(self.img_root, self.image_paths[idx])
        line_info = self.lines[idx]
        
        try:
            image = Image.open(img_path).convert('RGB')
            if self.transform:
                image = self.transform(image)
            return image, self.image_paths[idx], line_info
        except Exception as e:
            print(f"加载图像出错: {img_path}, 错误: {e}")
            return torch.zeros(3, 256, 256), self.image_paths[idx], line_info

def detect_all_faces_s3fd(detector, image_tensor, device, confidence_threshold=0.5):
    """
    使用S3FD检测器检测所有分数高于阈值的人脸。
    """
    try:
        input_tensor = (image_tensor.clone() + 1) * 127.5
        mean = torch.tensor([104., 117., 123.]).view(1, 3, 1, 1).to(device)
        processed_tensor = input_tensor[:, [2, 1, 0], :, :] - mean
        
        with torch.no_grad():
            detections = detector(processed_tensor)

        if detections and isinstance(detections, list) and len(detections) > 0:
            detection_dict = detections[0]
            if isinstance(detection_dict, dict):
                boxes = detection_dict.get('boxes')
                scores = detection_dict.get('scores')
                if boxes is not None and scores is not None and boxes.numel() > 0 and scores.numel() > 0:
                    mask = scores >= confidence_threshold
                    return boxes[mask].detach().cpu().numpy(), scores[mask].detach().cpu().numpy()
        return np.array([]), np.array([])
    except Exception as e:
        print(f"S3FD人脸检测出错: {e}")
        import traceback
        traceback.print_exc()
        return np.array([]), np.array([])

def crop_face(image_np, box, padding=0.1):
    """裁剪人脸区域，并添加一定的边距"""
    h, w = image_np.shape[:2]
    x1, y1, x2, y2 = box
    width, height = x2 - x1, y2 - y1
    pad_x, pad_y = int(width * padding), int(height * padding)
    x1, y1 = max(0, int(x1 - pad_x)), max(0, int(y1 - pad_y))
    x2, y2 = min(w, int(x2 + pad_x)), min(h, int(y2 + pad_y))
    face = image_np[y1:y2, x1:x2]
    return face.copy()

def process_dataset(txt_path, img_root, output_dir, output_txt, confidence_threshold=0.5, num_samples=None):
    """处理数据集，检测所有人脸，调整尺寸后以新编码命名并保存"""
    os.makedirs(output_dir, exist_ok=True)
    os.makedirs(os.path.dirname(os.path.abspath(output_txt)), exist_ok=True)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    print("初始化S3FD检测器...")
    detector = init_s3fd(device=device)
    print(f"S3FD检测器初始化完成，使用的置信度阈值: {confidence_threshold}")
    
    header_lines = []
    with open(txt_path, 'r') as f:
        all_lines = f.readlines()
        if len(all_lines) > 1:
            header_lines.append(all_lines[1].strip()) # 只保存属性名称行
    
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
    ])
    dataset = ImageDataset(txt_path, img_root, transform)
    print(f"数据集加载完成，共 {len(dataset)} 张图像")
    
    dataloader_subset = dataset
    if num_samples is not None and 0 < num_samples < len(dataset):
        indices = np.random.choice(len(dataset), num_samples, replace=False)
        dataloader_subset = torch.utils.data.Subset(dataset, indices)
        print(f"将随机处理 {num_samples} 张图像")
    else:
        print(f"将处理全部 {len(dataset)} 张图像")
    
    dataloader = torch.utils.data.DataLoader(dataloader_subset, batch_size=1, shuffle=False, num_workers=4)
    
    print("开始处理图像...")
    saved_face_counter = 1 # 新的文件名计数器，从1开始
    images_with_no_faces = 0
    cropped_image_lines = [] # 存储最终要写入txt的新行
    
    for image_tensor, original_img_path, original_line_info in tqdm(dataloader, desc="处理图像"):
        
        image_tensor = image_tensor.to(device)
        detected_boxes, _ = detect_all_faces_s3fd(detector, image_tensor, device, confidence_threshold)

        if len(detected_boxes) > 0:
            image_np = (image_tensor.squeeze(0).cpu().permute(1, 2, 0).numpy() + 1) / 2.0 * 255.0
            image_np = image_np.astype(np.uint8)

            for box in detected_boxes:
                h, w, _ = image_np.shape
                pixel_box = [box[0] * w, box[1] * h, box[2] * w, box[3] * h]
                face = crop_face(image_np, pixel_box)
                
                if face.size > 0:
                    # =============================================================================
                    #  核心改动区域
                    # =============================================================================
                    # 1. 调整尺寸
                    face_pil = Image.fromarray(face)
                    face_pil_resized = face_pil.resize((256, 256), Image.Resampling.LANCZOS)
                    
                    # 2. 生成新的顺序文件名
                    new_filename = f"{saved_face_counter:06d}.jpg"
                    output_path = os.path.join(output_dir, new_filename)
                    
                    # 保存调整尺寸后的图像
                    face_pil_resized.save(output_path)
                    
                    # 3. 生成新的标签行
                    # 提取原图的属性部分（-1和1的序列）
                    try:
                        # 分割一次，将原文件名和属性字符串分开
                        _, attributes_str = original_line_info[0].split(maxsplit=1)
                        # 用新文件名和旧属性拼接成新行
                        new_line_for_txt = f"{new_filename} {attributes_str}"
                        cropped_image_lines.append(new_line_for_txt)
                    except ValueError:
                        # 如果某行只有文件名没有属性，则跳过
                        print(f"警告：图像 {original_img_path[0]} 的标签行格式不正确，已跳过。")
                        continue

                    # 计数器加一
                    saved_face_counter += 1
                    # =============================================================================
        else:
            images_with_no_faces += 1

    print(f"正在创建输出txt文件: {output_txt}")
    with open(output_txt, 'w') as f:
        # 写入新的文件头：总人脸数 和 属性名
        f.write(f"{len(cropped_image_lines)}\n")
        if header_lines:
            f.write(f"{header_lines[0]}\n")
        
        # 写入所有新生成的标签行
        for line in cropped_image_lines:
            f.write(f"{line}\n")
    
    final_saved_count = saved_face_counter - 1
    print(f"\n处理完成! 共成功裁剪并保存 {final_saved_count} 张人脸图像。")
    print(f"有 {images_with_no_faces} 张原始图像未检测到符合条件的人脸。")
    print(f"裁剪后的图像保存在: {output_dir}")
    print(f"数据集标签文件保存在: {output_txt}")

def main():
    parser = argparse.ArgumentParser(description='使用S3FD检测所有人脸，调整尺寸后以新编码命名并保存')
    parser.add_argument('--txt_path', type=str, required=True, help='源数据集txt文件路径')
    parser.add_argument('--img_root', type=str, required=True, help='源图像根目录路径')
    parser.add_argument('--output_dir', type=str, required=True, help='裁剪后图像的输出目录路径')
    parser.add_argument('--output_txt', type=str, required=True, help='新生成的txt文件路径')
    parser.add_argument('--confidence', type=float, default=0.5, help='人脸检测置信度阈值 (默认: 0.5)')
    parser.add_argument('--num_samples', type=int, default=None, help='要处理的原始图像数量，不指定则处理全部')
    
    args = parser.parse_args()
    
    process_dataset(
        args.txt_path,
        args.img_root,
        args.output_dir,
        args.output_txt,
        args.confidence,
        args.num_samples
    )

if __name__ == "__main__":
    main()