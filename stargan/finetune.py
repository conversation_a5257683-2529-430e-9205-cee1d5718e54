import os
import argparse
import time
import datetime
from solver import Solver
from data_loader import get_loader
from torch.backends import cudnn
import torch
from torchvision.utils import save_image


def str2bool(v):
    return v.lower() in ('true')

def main(config):
    # 为了快速训练
    cudnn.benchmark = True

    # 创建不存在的目录
    if not os.path.exists(config.log_dir):
        os.makedirs(config.log_dir)
    if not os.path.exists(config.model_save_dir):
        os.makedirs(config.model_save_dir)
    if not os.path.exists(config.sample_dir):
        os.makedirs(config.sample_dir)
    if not os.path.exists(config.result_dir):
        os.makedirs(config.result_dir)

    # 数据加载器
    data_loader = None
    
    # 添加属性以兼容原始Solver类
    config.dataset = config.dataset_type
    config.num_iters = config.finetune_iters
    config.test_iters = config.pretrained_model if config.pretrained_model else 200000
    
    # 根据数据集类型加载数据
    if config.dataset_type == 'CelebA':
        data_loader = get_loader(config.image_dir, config.attr_path, config.selected_attrs,
                               config.celeba_crop_size, config.image_size, config.batch_size,
                               'CelebA', 'train', config.num_workers)
    elif config.dataset_type == 'RaFD':
        data_loader = get_loader(config.image_dir, None, None,
                               config.rafd_crop_size, config.image_size, config.batch_size,
                               'RaFD', 'train', config.num_workers)
    else:
        # 自定义CelebA格式数据集，使用CelebA加载器
        data_loader = get_loader(config.image_dir, config.attr_path, config.selected_attrs,
                               config.celeba_crop_size, config.image_size, config.batch_size,
                               'CelebA', 'train', config.num_workers)
        
    # 创建用于微调的求解器
    solver = FinetuneSolver(data_loader, config)
    
    # 开始微调
    solver.finetune()


class FinetuneSolver(Solver):
    """StarGAN微调类"""
    
    def __init__(self, data_loader, config):
        """初始化配置"""
        # 我们创建一个假的rafd_loader作为None，这样可以复用父类的初始化逻辑
        super(FinetuneSolver, self).__init__(data_loader, None, config)
        
        # 额外的微调配置
        self.pretrained_model = config.pretrained_model
        self.pretrained_model_dir = config.pretrained_model_dir
        self.finetune_iters = config.finetune_iters
        self.freeze_layers = config.freeze_layers
        self.dataset_type = config.dataset_type
        
        # 加载预训练模型
        if self.pretrained_model:
            self.restore_pretrained_model(self.pretrained_model)
            print(f'加载预训练模型: {self.pretrained_model}')
        
        # 如果需要冻结部分层
        if self.freeze_layers:
            self.freeze_model_layers()
    
    def restore_pretrained_model(self, pretrained_model):
        """从自定义目录恢复训练好的生成器和判别器"""
        print('从步骤 {}加载预训练模型...'.format(pretrained_model))
        
        # 使用自定义的预训练模型目录或默认目录
        model_dir = self.pretrained_model_dir if self.pretrained_model_dir else self.model_save_dir
        
        G_path = os.path.join(model_dir, '{}-G.ckpt'.format(pretrained_model))
        D_path = os.path.join(model_dir, '{}-D.ckpt'.format(pretrained_model))
        
        print(f'加载生成器模型: {G_path}')
        print(f'加载判别器模型: {D_path}')
        
        # 检查文件是否存在
        if not os.path.exists(G_path):
            raise FileNotFoundError(f"生成器模型文件未找到: {G_path}")
        if not os.path.exists(D_path):
            raise FileNotFoundError(f"判别器模型文件未找到: {D_path}")
            
        self.load_model_weights(self.G, G_path)
        self.D.load_state_dict(torch.load(D_path, map_location=lambda storage, loc: storage))
    
    def freeze_model_layers(self):
        """冻结生成器的部分层"""
        # 示例：冻结编码器部分
        print("冻结生成器的部分层...")
        
        # 获取所有层的名称
        for name, param in self.G.named_parameters():
            # 根据名称冻结特定层，这里的逻辑根据模型结构和需求调整
            if "encoder" in name or "main.0" in name or "main.1" in name or "main.2" in name:
                param.requires_grad = False
                print(f"冻结层: {name}")
    
    def finetune(self):
        """在新数据集上微调StarGAN模型"""
        # 设置数据加载器
        data_loader = self.celeba_loader
        
        # 获取用于调试的固定输入
        data_iter = iter(data_loader)
        x_fixed, c_org = next(data_iter)
        x_fixed = x_fixed.to(self.device)
        
        # 创建标签
        if self.dataset_type in ['CelebA', 'Custom']:
            c_dim = self.c_dim
            c_fixed_list = self.create_labels(c_org, c_dim, 'CelebA', self.selected_attrs)
        else:
            c_dim = self.c_dim
            c_fixed_list = self.create_labels(c_org, c_dim, 'RaFD')
        
        # 用于学习率衰减的缓存
        g_lr = self.g_lr * 0.1  # 开始时使用较低学习率
        d_lr = self.d_lr * 0.1
        warmup_iters = min(1000, self.finetune_iters // 10)  # 预热迭代次数
        
        # 开始微调
        print('开始微调...')
        start_iters = 0
        start_time = time.time()
        
        # 显示哪些参数正在被训练
        print("可训练的参数:")
        for name, param in self.G.named_parameters():
            if param.requires_grad:
                print(f"  {name}")
        
        for i in range(start_iters, self.finetune_iters):
            
            # 学习率预热
            if i < warmup_iters:
                # 逐渐增加学习率
                curr_g_lr = self.g_lr * (i + 1) / warmup_iters
                curr_d_lr = self.d_lr * (i + 1) / warmup_iters
                self.update_lr(curr_g_lr, curr_d_lr)
            
            # =================================================================================== #
            #                             1. 预处理输入数据                                        #
            # =================================================================================== #
            
            # 获取真实图像和标签
            try:
                x_real, label_org = next(data_iter)
            except:
                data_iter = iter(data_loader)
                x_real, label_org = next(data_iter)
            
            # 随机生成目标域标签
            rand_idx = torch.randperm(label_org.size(0))
            label_trg = label_org[rand_idx]
            
            if self.dataset_type in ['CelebA', 'Custom']:
                c_org = label_org.clone()
                c_trg = label_trg.clone()
            else:
                c_org = self.label2onehot(label_org, c_dim)
                c_trg = self.label2onehot(label_trg, c_dim)
            
            x_real = x_real.to(self.device)       # 输入图像
            c_org = c_org.to(self.device)         # 原始域标签
            c_trg = c_trg.to(self.device)         # 目标域标签
            label_org = label_org.to(self.device) # 用于计算分类损失的标签
            label_trg = label_trg.to(self.device) # 用于计算分类损失的标签
            
            # =================================================================================== #
            #                             2. 训练判别器                                           #
            # =================================================================================== #
            
            # 计算真实图像的损失
            out_src, out_cls = self.D(x_real)
            d_loss_real = -torch.mean(out_src)
            d_loss_cls = self.classification_loss(out_cls, label_org, 'CelebA' if self.dataset_type in ['CelebA', 'Custom'] else 'RaFD')
            
            # 计算假图像的损失
            x_fake, _, _ = self.G(x_real, c_trg)
            out_src, _ = self.D(x_fake.detach())
            d_loss_fake = torch.mean(out_src)
            
            # 计算梯度惩罚的损失
            alpha = torch.rand(x_real.size(0), 1, 1, 1).to(self.device)
            x_hat = (alpha * x_real.data + (1 - alpha) * x_fake.data).requires_grad_(True)
            out_src, _ = self.D(x_hat)
            d_loss_gp = self.gradient_penalty(out_src, x_hat)
            
            # 反向传播和优化
            d_loss = d_loss_real + d_loss_fake + self.lambda_cls * d_loss_cls + self.lambda_gp * d_loss_gp
            self.reset_grad()
            d_loss.backward()
            self.d_optimizer.step()
            
            # 日志记录
            loss = {}
            loss['D/loss_real'] = d_loss_real.item()
            loss['D/loss_fake'] = d_loss_fake.item()
            loss['D/loss_cls'] = d_loss_cls.item()
            loss['D/loss_gp'] = d_loss_gp.item()
            
            # =================================================================================== #
            #                             3. 训练生成器                                           #
            # =================================================================================== #
            
            if (i+1) % self.n_critic == 0:
                # 计算为判别器欺骗的损失
                x_fake, _, _ = self.G(x_real, c_trg)
                out_src, out_cls = self.D(x_fake)
                g_loss_fake = -torch.mean(out_src)
                g_loss_cls = self.classification_loss(out_cls, label_trg, 'CelebA' if self.dataset_type in ['CelebA', 'Custom'] else 'RaFD')
                
                # 计算重建损失
                x_reconst, _, _ = self.G(x_fake, c_org)
                g_loss_rec = torch.mean(torch.abs(x_real - x_reconst))
                
                # 反向传播和优化
                g_loss = g_loss_fake + self.lambda_rec * g_loss_rec + self.lambda_cls * g_loss_cls
                self.reset_grad()
                g_loss.backward()
                self.g_optimizer.step()
                
                # 日志记录
                loss['G/loss_fake'] = g_loss_fake.item()
                loss['G/loss_rec'] = g_loss_rec.item()
                loss['G/loss_cls'] = g_loss_cls.item()
            
            # =================================================================================== #
            #                                 4. 其他操作                                         #
            # =================================================================================== #
            
            # 输出训练状态
            if (i+1) % self.log_step == 0:
                et = time.time() - start_time
                et = str(datetime.timedelta(seconds=et))[:-7]
                log = "微调进度: [{}/{}], 已用时: {}, 数据集: {}".format(
                    i+1, self.finetune_iters, et, self.dataset_type)
                for tag, value in loss.items():
                    log += ", {}: {:.4f}".format(tag, value)
                print(log)
            
            # 转换固定图像用于调试
            if (i+1) % self.sample_step == 0:
                with torch.no_grad():
                    x_fake_list = [x_fixed]
                    for c_fixed in c_fixed_list:
                        x_fake, _, _ = self.G(x_fixed, c_fixed)
                        x_fake_list.append(x_fake)
                    x_concat = torch.cat(x_fake_list, dim=3)
                    sample_path = os.path.join(self.sample_dir, 'finetune-{}-images.jpg'.format(i+1))
                    save_image(self.denorm(x_concat.data.cpu()), sample_path, nrow=1, padding=0)
                    print('保存真实和伪造图像到 {}...'.format(sample_path))
            
            # 保存模型检查点
            if (i+1) % self.model_save_step == 0:
                G_path = os.path.join(self.model_save_dir, '{}-G.ckpt'.format(i+1))
                D_path = os.path.join(self.model_save_dir, '{}-D.ckpt'.format(i+1))
                torch.save(self.G.state_dict(), G_path)
                torch.save(self.D.state_dict(), D_path)
                print('保存模型检查点到 {}...'.format(self.model_save_dir))
                
            # 衰减学习率
            if (i+1) % self.lr_update_step == 0 and (i+1) > (self.finetune_iters - self.num_iters_decay):
                g_lr -= (self.g_lr / float(self.num_iters_decay))
                d_lr -= (self.d_lr / float(self.num_iters_decay))
                self.update_lr(g_lr, d_lr)
                print('衰减学习率, g_lr: {}, d_lr: {}.'.format(g_lr, d_lr))


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    
    # 模型配置
    parser.add_argument('--c_dim', type=int, default=5, help='目标域标签的维度')
    parser.add_argument('--celeba_crop_size', type=int, default=178, help='CelebA数据集的裁剪大小')
    parser.add_argument('--rafd_crop_size', type=int, default=256, help='RaFD数据集的裁剪大小') 
    parser.add_argument('--crop_size', type=int, default=178, help='自定义数据集的裁剪大小')
    parser.add_argument('--image_size', type=int, default=256, help='图像分辨率')
    parser.add_argument('--g_conv_dim', type=int, default=64, help='G第一层的卷积过滤器数量')
    parser.add_argument('--d_conv_dim', type=int, default=64, help='D第一层的卷积过滤器数量')
    parser.add_argument('--g_repeat_num', type=int, default=6, help='G中残差块的数量')
    parser.add_argument('--d_repeat_num', type=int, default=6, help='D中步长卷积层的数量')
    parser.add_argument('--lambda_cls', type=float, default=1, help='域分类损失的权重')
    parser.add_argument('--lambda_rec', type=float, default=10, help='重建损失的权重')
    parser.add_argument('--lambda_gp', type=float, default=10, help='梯度惩罚的权重')
    
    # 微调配置
    parser.add_argument('--dataset_type', type=str, default='CelebA', choices=['CelebA', 'RaFD', 'Custom'])
    parser.add_argument('--batch_size', type=int, default=8, help='小批量大小')
    parser.add_argument('--finetune_iters', type=int, default=20000, help='微调迭代次数')
    parser.add_argument('--num_iters_decay', type=int, default=100, help='学习率衰减的迭代次数')
    parser.add_argument('--g_lr', type=float, default=0.001, help='G的学习率')
    parser.add_argument('--d_lr', type=float, default=0.001, help='D的学习率')
    parser.add_argument('--n_critic', type=int, default=1, help='每次G更新前D更新的次数')
    parser.add_argument('--beta1', type=float, default=0.5, help='Adam优化器的beta1')
    parser.add_argument('--beta2', type=float, default=0.999, help='Adam优化器的beta2')
    parser.add_argument('--pretrained_model', type=int, default=None, help='预训练模型的迭代次数')
    parser.add_argument('--pretrained_model_dir', type=str, default='/root/tf-logs/FOUND_code/stargan/stargan_celeba_256/models', help='预训练模型的目录')
    parser.add_argument('--resume_iters', type=int, default=None, help='从哪个迭代恢复训练')
    parser.add_argument('--freeze_layers', type=str2bool, default=False, help='是否冻结G的某些层')
    
    # CelebA的特定配置
    parser.add_argument('--selected_attrs', '--list', nargs='+', help='CelebA数据集的选定属性',
                        default=['Black_Hair', 'Blond_Hair', 'Brown_Hair', 'Male', 'Young'])
    
    # 其他配置
    parser.add_argument('--num_workers', type=int, default=1)
    parser.add_argument('--mode', type=str, default='train', choices=['train', 'test'])
    parser.add_argument('--use_tensorboard', type=str2bool, default=False)
    
    # 目录
    parser.add_argument('--image_dir', type=str, default='data/custom/images')
    parser.add_argument('--attr_path', type=str, default='data/celeba/list_attr_celeba.txt')
    parser.add_argument('--log_dir', type=str, default='stargan/logs')
    parser.add_argument('--model_save_dir', type=str, default='stargan/models_test')
    parser.add_argument('--sample_dir', type=str, default='stargan/samples')
    parser.add_argument('--result_dir', type=str, default='stargan/results')
    
    # 步长大小
    parser.add_argument('--log_step', type=int, default=10)
    parser.add_argument('--sample_step', type=int, default=500)
    parser.add_argument('--model_save_step', type=int, default=100)
    parser.add_argument('--lr_update_step', type=int, default=1000)
    
    # 该变量已从原始模型中移除，因为微调不需要它
    parser.add_argument('--c2_dim', type=int, default=8, help='RaFD的域标签的维度')
    
    config = parser.parse_args()
    print(config)
    main(config)
    
    
# python finetune.py \
#   --dataset_type CelebA \
#   --image_dir /root/autodl-tmp/img_align_celeba_watermarked_cropped_s3fd \
#   --attr_path /root/autodl-tmp/list_attr_celeba_watermarked_cropped_s3fd.txt \
#   --pretrained_model 200000 \
#   --pretrained_model_dir /root/tf-logs/FOUND_code/stargan/stargan_celeba_256/models \
#   --finetune_iters 10000 \
#   --batch_size 8 
